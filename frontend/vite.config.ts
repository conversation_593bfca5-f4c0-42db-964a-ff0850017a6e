import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import path from 'path'

// https://vite.dev/config/ - 基于Context7 Tailwind CSS v4最佳实践
export default defineConfig({
  plugins: [
    tailwindcss(), // Tailwind CSS v4 Vite插件
    react()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 5174,
    host: true,
    proxy: {
      // API代理配置 - 将 /api/* 请求代理到后端服务器
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        secure: false,
        // 保持原始路径，不重写
        // rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
})
