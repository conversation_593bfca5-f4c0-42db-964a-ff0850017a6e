/**
 * 租户管理API服务
 */

import type { PageResult } from '@/types'
import type { 
  Tenant, 
  TenantQueryRequest, 
  TenantCreateRequest, 
  TenantUpdateRequest,
  TenantStats,
  TenantConfig,
  TenantUserAssignRequest
} from '../types/tenant'
import { httpClient } from './request'

/**
 * 租户管理服务
 */
export class TenantService {
  /**
   * 分页查询租户列表
   * @param params 查询参数
   * @returns 分页结果
   */
  static async pageTenants(params: TenantQueryRequest): Promise<PageResult<Tenant>> {
    console.log('🚀 发送分页查询租户请求:', params)
    const response = await httpClient.get<PageResult<Tenant>>('/system/tenant/page', { params })
    console.log('✅ 分页查询租户响应:', response)
    return response
  }

  /**
   * 获取所有启用的租户
   * @returns 租户列表
   */
  static async getAllEnabledTenants(): Promise<Tenant[]> {
    console.log('🚀 发送获取所有启用租户请求')
    const response = await httpClient.get<Tenant[]>('/system/tenant/enabled')
    console.log('✅ 获取所有启用租户响应:', response)
    return response
  }

  /**
   * 根据ID获取租户详情
   * @param id 租户ID
   * @returns 租户信息
   */
  static async getTenantById(id: number): Promise<Tenant> {
    console.log('🚀 发送获取租户详情请求:', id)
    const response = await httpClient.get<Tenant>(`/system/tenant/${id}`)
    console.log('✅ 获取租户详情响应:', response)
    return response
  }

  /**
   * 创建租户
   * @param data 创建数据
   * @returns 创建结果
   */
  static async createTenant(data: TenantCreateRequest): Promise<Tenant> {
    console.log('🚀 发送创建租户请求:', data)
    const response = await httpClient.post<Tenant>('/system/tenant', data)
    console.log('✅ 创建租户响应:', response)
    return response
  }

  /**
   * 更新租户
   * @param data 更新数据
   * @returns 更新结果
   */
  static async updateTenant(data: TenantUpdateRequest): Promise<Tenant> {
    console.log('🚀 发送更新租户请求:', data)
    const response = await httpClient.put<Tenant>(`/system/tenant/${data.id}`, data)
    console.log('✅ 更新租户响应:', response)
    return response
  }

  /**
   * 删除租户
   * @param id 租户ID
   * @returns 删除结果
   */
  static async deleteTenant(id: number): Promise<void> {
    console.log('🚀 发送删除租户请求:', id)
    const response = await httpClient.delete<void>(`/system/tenant/${id}`)
    console.log('✅ 删除租户响应:', response)
    return response
  }

  /**
   * 启用租户
   * @param id 租户ID
   * @returns 启用结果
   */
  static async enableTenant(id: number): Promise<void> {
    console.log('🚀 发送启用租户请求:', id)
    const params = new URLSearchParams()
    params.append('status', '1')
    const response = await httpClient.put<void>(`/system/tenant/${id}/status`, null, { params })
    console.log('✅ 启用租户响应:', response)
    return response
  }

  /**
   * 禁用租户
   * @param id 租户ID
   * @returns 禁用结果
   */
  static async disableTenant(id: number): Promise<void> {
    console.log('🚀 发送禁用租户请求:', id)
    const params = new URLSearchParams()
    params.append('status', '0')
    const response = await httpClient.put<void>(`/system/tenant/${id}/status`, null, { params })
    console.log('✅ 禁用租户响应:', response)
    return response
  }

  /**
   * 获取租户统计信息
   * @param id 租户ID
   * @returns 统计信息
   */
  static async getTenantStats(id: number): Promise<TenantStats> {
    console.log('🚀 发送获取租户统计请求:', id)
    const response = await httpClient.get<TenantStats>(`/system/tenant/${id}/stats`)
    console.log('✅ 获取租户统计响应:', response)
    return response
  }

  /**
   * 获取租户配置
   * @param id 租户ID
   * @returns 配置列表
   */
  static async getTenantConfigs(id: number): Promise<TenantConfig[]> {
    console.log('🚀 发送获取租户配置请求:', id)
    const response = await httpClient.get<TenantConfig[]>(`/system/tenant/${id}/configs`)
    console.log('✅ 获取租户配置响应:', response)
    return response
  }

  /**
   * 更新租户配置
   * @param id 租户ID
   * @param configs 配置列表
   * @returns 更新结果
   */
  static async updateTenantConfigs(id: number, configs: TenantConfig[]): Promise<void> {
    console.log('🚀 发送更新租户配置请求:', { id, configs })
    const response = await httpClient.put<void>(`/system/tenant/${id}/configs`, configs)
    console.log('✅ 更新租户配置响应:', response)
    return response
  }

  /**
   * 获取租户用户列表
   * @param id 租户ID
   * @returns 用户列表
   */
  static async getTenantUsers(id: number): Promise<any[]> {
    console.log('🚀 发送获取租户用户请求:', id)
    const response = await httpClient.get<any[]>(`/system/tenant/${id}/users`)
    console.log('✅ 获取租户用户响应:', response)
    return response
  }

  /**
   * 获取可分配给租户的用户列表
   * @param id 租户ID
   * @returns 可分配用户列表
   */
  static async getAvailableUsersForTenant(id: number): Promise<any[]> {
    console.log('🚀 发送获取可分配用户请求:', id)
    const response = await httpClient.get<any[]>(`/system/tenant/${id}/available-users`)
    console.log('✅ 获取可分配用户响应:', response)
    return response
  }

  /**
   * 分配用户到租户
   * @param data 分配数据
   * @returns 分配结果
   */
  static async assignUsersToTenant(data: TenantUserAssignRequest): Promise<void> {
    console.log('🚀 发送分配用户到租户请求:', data)
    const response = await httpClient.post<void>('/system/tenant/assign-users', data)
    console.log('✅ 分配用户到租户响应:', response)
    return response
  }

  /**
   * 从租户移除用户
   * @param tenantId 租户ID
   * @param userId 用户ID
   * @returns 移除结果
   */
  static async removeUserFromTenant(tenantId: number, userId: number): Promise<void> {
    console.log('🚀 发送从租户移除用户请求:', { tenantId, userId })
    const params = new URLSearchParams()
    params.append('tenantId', tenantId.toString())
    params.append('userId', userId.toString())
    const response = await httpClient.delete<void>('/system/tenant/remove-user', { params })
    console.log('✅ 从租户移除用户响应:', response)
    return response
  }

  /**
   * 检查租户编码是否存在
   * @param tenantCode 租户编码
   * @param excludeId 排除的租户ID（编辑时使用）
   * @returns 是否存在
   */
  static async checkTenantCodeExists(tenantCode: string, excludeId?: number): Promise<boolean> {
    console.log('🚀 发送检查租户编码请求:', { tenantCode, excludeId })
    const params = new URLSearchParams()
    params.append('tenantCode', tenantCode)
    if (excludeId) {
      params.append('excludeId', excludeId.toString())
    }
    const response = await httpClient.get<boolean>('/system/tenant/check-code', { params })
    console.log('✅ 检查租户编码响应:', response)
    return response
  }

  /**
   * 导出租户数据
   * @param tenantIds 租户ID列表
   * @returns 导出文件
   */
  static async exportTenants(tenantIds?: number[]): Promise<Blob> {
    console.log('🚀 发送导出租户请求:', tenantIds)
    const params = new URLSearchParams()
    if (tenantIds && tenantIds.length > 0) {
      tenantIds.forEach(id => params.append('tenantIds', id.toString()))
    }
    const response = await httpClient.get<Blob>('/system/tenant/export', { 
      params,
      responseType: 'blob'
    })
    console.log('✅ 导出租户响应:', response)
    return response
  }
}
