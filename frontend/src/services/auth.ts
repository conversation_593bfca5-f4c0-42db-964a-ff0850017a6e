import { httpClient } from './request.ts'
import type { LoginRequest, LoginResponse, User, ApiResponse, Menu } from '../types'

/**
 * 后端菜单数据结构（实际返回的格式）
 */
interface BackendMenu {
  id: number
  parentId?: number
  name: string          // 后端实际返回的字段名
  title: string         // 后端实际返回的字段名
  path?: string
  component?: string
  icon?: string
  permission?: string   // 后端实际返回的字段名
  type: number          // 后端实际返回的字段名
  sort: number          // 后端实际返回的字段名
  visible: boolean      // 后端实际返回的布尔值
  enabled: boolean      // 后端实际返回的布尔值
  children?: BackendMenu[]
}

/**
 * 转换后端菜单数据为前端格式
 * 注意：后端已经返回了正确的格式，只需要递归处理子菜单
 */
const convertBackendMenuToFrontend = (backendMenu: BackendMenu): Menu => {
  console.log('convertBackendMenuToFrontend - 输入:', backendMenu)

  const converted = {
    id: backendMenu.id,
    parentId: backendMenu.parentId,
    name: backendMenu.name,           // 直接使用后端返回的 name
    title: backendMenu.title,         // 直接使用后端返回的 title
    path: backendMenu.path,
    component: backendMenu.component,
    icon: backendMenu.icon,
    permission: backendMenu.permission, // 直接使用后端返回的 permission
    type: backendMenu.type,           // 直接使用后端返回的 type
    sort: backendMenu.sort,           // 直接使用后端返回的 sort
    visible: backendMenu.visible,     // 直接使用后端返回的布尔值
    enabled: backendMenu.enabled,     // 直接使用后端返回的布尔值
    children: backendMenu.children ? backendMenu.children.map(convertBackendMenuToFrontend) : []
  }

  console.log('convertBackendMenuToFrontend - 输出:', converted)
  return converted
}

/**
 * 认证相关API服务
 */
export class AuthService {
  /**
   * 用户登录
   * @param loginData 登录数据
   * @returns 登录响应
   */
  static async login(loginData: LoginRequest): Promise<LoginResponse> {
    return httpClient.post<LoginResponse>('/auth/login', loginData, {
      skipAuth: true, // 登录请求跳过认证
    })
  }

  /**
   * 用户登出
   * @returns 登出响应
   */
  static async logout(): Promise<ApiResponse<void>> {
    return httpClient.post<void>('/auth/logout')
  }

  /**
   * 检查登录状态
   * @returns 登录状态信息
   */
  static async checkLogin(): Promise<ApiResponse<{
    isLogin: boolean
    userId?: number
    tokenValue?: string
    tokenTimeout?: number
  }>> {
    return httpClient.get('/auth/check')
  }

  /**
   * 获取当前用户信息
   * @returns 用户信息
   */
  static async getCurrentUser(): Promise<ApiResponse<User>> {
    return httpClient.get<User>('/auth/user')
  }

  /**
   * 获取用户权限
   * @returns 权限列表
   */
  static async getUserPermissions(): Promise<ApiResponse<string[]>> {
    return httpClient.get<string[]>('/auth/permissions')
  }

  /**
   * 获取用户角色
   * @returns 角色列表
   */
  static async getUserRoles(): Promise<ApiResponse<string[]>> {
    return httpClient.get<string[]>('/auth/roles')
  }

  /**
   * 刷新Token
   * @returns 新的Token信息
   */
  static async refreshToken(): Promise<ApiResponse<{
    token: string
    expires: number
  }>> {
    return httpClient.post('/auth/refresh')
  }

  /**
   * 获取用户菜单
   * @returns 用户菜单列表
   */
  static async getUserMenus(): Promise<Menu[]> {
    const response = await httpClient.get<BackendMenu[]>('/auth/menus')

    console.log('AuthService.getUserMenus - 原始响应:', response)
    console.log('AuthService.getUserMenus - 第一个菜单项:', response[0])

    // 转换后端数据为前端格式
    const convertedMenus = response.map(convertBackendMenuToFrontend)

    console.log('AuthService.getUserMenus - 转换后数据:', convertedMenus)
    console.log('AuthService.getUserMenus - 第一个转换后菜单:', convertedMenus[0])

    return convertedMenus
  }

  /**
   * 修改密码
   * @param data 密码修改数据
   * @returns 修改结果
   */
  static async changePassword(data: {
    oldPassword: string
    newPassword: string
    confirmPassword: string
  }): Promise<ApiResponse<void>> {
    return httpClient.post<void>('/auth/change-password', data)
  }
}
