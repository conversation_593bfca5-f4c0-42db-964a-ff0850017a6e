import { httpClient } from './request'
import type { PageResult } from '../types/api'
import type {
  User,
  UserQueryRequest,
  UserCreateRequest,
  UserUpdateRequest
} from '../types/user'



/**
 * 用户管理API服务
 */
export class UserService {
  /**
   * 分页查询用户列表
   * @param params 查询参数
   * @returns 用户分页数据
   */
  static async pageUsers(params: UserQueryRequest): Promise<PageResult<User>> {
    console.log('🚀 发送用户分页查询请求:', params)
    const response = await httpClient.get<PageResult<User>>('/system/user/page', { params })
    console.log('✅ 用户分页查询响应:', response)
    return response
  }

  /**
   * 根据ID获取用户详情（脱敏版本，用于查看）
   * @param id 用户ID
   * @returns 用户详情
   */
  static async getUserById(id: number): Promise<User> {
    console.log('🚀 发送获取用户详情请求:', id)
    const response = await httpClient.get<User>(`/system/user/${id}`)
    console.log('✅ 获取用户详情响应:', response)
    return response
  }

  /**
   * 根据ID获取用户编辑信息（原始版本，用于编辑）
   * @param id 用户ID
   * @returns 用户详情
   */
  static async getUserForEdit(id: number): Promise<User> {
    console.log('🚀 发送获取用户编辑信息请求:', id)
    const response = await httpClient.get<User>(`/system/user/${id}/edit`)
    console.log('✅ 获取用户编辑信息响应:', response)
    return response
  }

  /**
   * 创建用户
   * @param userData 用户数据
   * @returns 创建结果
   */
  static async createUser(userData: UserCreateRequest): Promise<void> {
    console.log('🚀 发送创建用户请求:', userData)
    const response = await httpClient.post<void>('/system/user', userData)
    console.log('✅ 创建用户响应:', response)
    return response
  }

  /**
   * 更新用户
   * @param userData 用户数据
   * @returns 更新结果
   */
  static async updateUser(userData: UserUpdateRequest): Promise<void> {
    console.log('🚀 发送更新用户请求:', userData)
    const response = await httpClient.put<void>('/system/user', userData)
    console.log('✅ 更新用户响应:', response)
    return response
  }

  /**
   * 删除用户
   * @param id 用户ID
   * @returns 删除结果
   */
  static async deleteUser(id: number): Promise<ApiResponse<void>> {
    return httpClient.delete<void>(`/system/user/${id}`)
  }

  /**
   * 批量删除用户
   * @param ids 用户ID数组
   * @returns 删除结果
   */
  static async batchDeleteUsers(ids: number[]): Promise<ApiResponse<void>> {
    return httpClient.delete<void>('/system/user/batch', { data: { ids } })
  }

  /**
   * 重置用户密码
   * @param data 重置密码数据
   * @returns 重置结果
   */
  static async resetPassword(data: { id: number; newPassword: string }): Promise<void> {
    console.log('🚀 发送重置密码请求:', { id: data.id, password: '***' })
    const response = await httpClient.put<void>('/system/user/reset-password', data)
    console.log('✅ 重置密码响应:', response)
    return response
  }

  /**
   * 启用用户
   * @param id 用户ID
   * @returns 启用结果
   */
  static async enableUser(id: number): Promise<void> {
    console.log('🚀 发送启用用户请求:', id)
    const response = await httpClient.put<void>(`/system/user/${id}/enable`)
    console.log('✅ 启用用户响应:', response)
    return response
  }

  /**
   * 禁用用户
   * @param id 用户ID
   * @returns 禁用结果
   */
  static async disableUser(id: number): Promise<void> {
    console.log('🚀 发送禁用用户请求:', id)
    const response = await httpClient.put<void>(`/system/user/${id}/disable`)
    console.log('✅ 禁用用户响应:', response)
    return response
  }

  /**
   * 切换用户状态（兼容方法）
   * @param id 用户ID
   * @param status 状态（0-禁用，1-启用）
   * @returns 操作结果
   */
  static async toggleUserStatus(id: number, status: number): Promise<void> {
    console.log('🚀 发送切换用户状态请求:', { id, status })
    const response = await httpClient.put<void>(`/system/user/${id}/status`, { status })
    console.log('✅ 切换用户状态响应:', response)
    return response
  }

  /**
   * 分配用户角色
   * @param userId 用户ID
   * @param roleIds 角色ID数组
   * @returns 分配结果
   */
  static async assignRoles(userId: number, roleIds: number[]): Promise<ApiResponse<void>> {
    return httpClient.post<void>(`/system/user/${userId}/roles`, { roleIds })
  }

  /**
   * 获取用户角色列表
   * @param userId 用户ID
   * @returns 角色ID数组
   */
  static async getUserRoles(userId: number): Promise<number[]> {
    console.log('🚀 发送获取用户角色请求:', userId)
    const response = await httpClient.get<number[]>(`/system/user/${userId}/roles`)
    console.log('✅ 获取用户角色响应:', response)
    return response
  }

  /**
   * 检查用户名是否存在
   * @param username 用户名
   * @param excludeId 排除的用户ID（用于编辑时检查）
   * @returns 是否存在
   */
  static async checkUsername(username: string, excludeId?: number): Promise<ApiResponse<boolean>> {
    return httpClient.get<boolean>('/system/user/check-username', {
      params: { username, excludeId }
    })
  }

  /**
   * 检查邮箱是否存在
   * @param email 邮箱
   * @param excludeId 排除的用户ID（用于编辑时检查）
   * @returns 是否存在
   */
  static async checkEmail(email: string, excludeId?: number): Promise<ApiResponse<boolean>> {
    return httpClient.get<boolean>('/system/user/check-email', {
      params: { email, excludeId }
    })
  }

  /**
   * 检查手机号是否存在
   * @param phone 手机号
   * @param excludeId 排除的用户ID（用于编辑时检查）
   * @returns 是否存在
   */
  static async checkPhone(phone: string, excludeId?: number): Promise<ApiResponse<boolean>> {
    return httpClient.get<boolean>('/system/user/check-phone', {
      params: { phone, excludeId }
    })
  }

  /**
   * 导出用户数据
   * @param params 查询参数
   * @returns 导出文件
   */
  static async exportUsers(params: UserQueryRequest): Promise<Blob> {
    const response = await httpClient.instance.get('/system/user/export', {
      params,
      responseType: 'blob',
    })
    return response.data
  }

  /**
   * 导入用户数据
   * @param file 导入文件
   * @returns 导入结果
   */
  static async importUsers(file: File): Promise<ApiResponse<{
    total: number
    success: number
    failed: number
    errors?: string[]
  }>> {
    const formData = new FormData()
    formData.append('file', file)
    return httpClient.upload('/system/user/import', formData)
  }

  /**
   * 同步主播用户
   * 从vim_user表同步主播用户到sys_user表
   * @returns 同步结果
   */
  static async syncAnchorUsers(): Promise<string> {
    console.log('🚀 发送同步主播用户请求')
    const response = await httpClient.post<string>('/system/user/sync-anchor-users')
    console.log('✅ 同步主播用户响应:', response)
    return response
  }
}
