/**
 * 部门管理服务
 * 
 * 提供部门相关的API调用功能
 */

import { httpClient } from './request'
import type { PageResult } from '../types/api'
import type { 
  Dept, 
  DeptQueryRequest, 
  DeptCreateRequest, 
  DeptUpdateRequest,
  DeptMoveRequest,
  UserDeptAssignRequest,
  DeptStats
} from '../types/dept'

/**
 * 部门管理服务类
 */
export class DeptService {
  
  /**
   * 分页查询部门列表
   * @param params 查询参数
   * @returns 部门分页列表
   */
  static async pageDepts(params: DeptQueryRequest): Promise<PageResult<Dept>> {
    console.log('🚀 发送部门分页查询请求:', params)
    const response = await httpClient.get<PageResult<Dept>>('/system/dept/page', { params })
    console.log('✅ 部门分页查询响应:', response)
    return response
  }

  /**
   * 获取部门详情
   * @param id 部门ID
   * @returns 部门详情
   */
  static async getDeptById(id: number): Promise<Dept> {
    console.log('🚀 发送获取部门详情请求:', id)
    const response = await httpClient.get<Dept>(`/system/dept/${id}`)
    console.log('✅ 获取部门详情响应:', response)
    return response
  }

  /**
   * 创建部门
   * @param data 部门创建数据
   * @returns 创建结果
   */
  static async createDept(data: DeptCreateRequest): Promise<void> {
    console.log('🚀 发送创建部门请求:', data)
    const response = await httpClient.post<void>('/system/dept', data)
    console.log('✅ 创建部门响应:', response)
    return response
  }

  /**
   * 更新部门
   * @param data 部门更新数据
   * @returns 更新结果
   */
  static async updateDept(data: DeptUpdateRequest): Promise<void> {
    console.log('🚀 发送更新部门请求:', data)
    const response = await httpClient.put<void>('/system/dept', data)
    console.log('✅ 更新部门响应:', response)
    return response
  }

  /**
   * 删除部门
   * @param id 部门ID
   * @returns 删除结果
   */
  static async deleteDept(id: number): Promise<void> {
    console.log('🚀 发送删除部门请求:', id)
    const response = await httpClient.delete<void>(`/system/dept/${id}`)
    console.log('✅ 删除部门响应:', response)
    return response
  }

  /**
   * 批量删除部门
   * @param ids 部门ID数组
   * @returns 删除结果
   */
  static async batchDeleteDepts(ids: number[]): Promise<void> {
    console.log('🚀 发送批量删除部门请求:', ids)
    const response = await httpClient.delete<void>('/system/dept/batch', { data: { ids } })
    console.log('✅ 批量删除部门响应:', response)
    return response
  }

  /**
   * 获取所有启用的部门
   * @returns 启用的部门列表
   */
  static async getAllEnabledDepts(): Promise<Dept[]> {
    console.log('🚀 发送获取启用部门请求')
    const response = await httpClient.get<Dept[]>('/system/dept/enabled')
    console.log('✅ 获取启用部门响应:', response)
    return response
  }

  /**
   * 根据父部门ID获取子部门
   * @param parentId 父部门ID
   * @returns 子部门列表
   */
  static async getDeptsByParentId(parentId: number): Promise<Dept[]> {
    console.log('🚀 发送获取子部门请求:', parentId)
    const response = await httpClient.get<Dept[]>(`/system/dept/children/${parentId}`)
    console.log('✅ 获取子部门响应:', response)
    return response
  }

  /**
   * 获取部门树
   * @returns 部门树结构
   */
  static async getDeptTree(): Promise<Dept[]> {
    console.log('🚀 发送获取部门树请求')
    const response = await httpClient.get<Dept[]>('/system/dept/tree')
    console.log('✅ 获取部门树响应:', response)
    return response
  }

  /**
   * 移动部门（更改父部门）
   * @param data 移动数据
   * @returns 移动结果
   */
  static async moveDept(data: DeptMoveRequest): Promise<void> {
    console.log('🚀 发送移动部门请求:', data)
    const response = await httpClient.put<void>('/system/dept/move', data)
    console.log('✅ 移动部门响应:', response)
    return response
  }

  /**
   * 启用部门
   * @param id 部门ID
   * @returns 启用结果
   */
  static async enableDept(id: number): Promise<void> {
    console.log('🚀 发送启用部门请求:', id)
    const params = new URLSearchParams()
    params.append('status', '1')
    const response = await httpClient.put<void>(`/system/dept/${id}/status`, null, { params })
    console.log('✅ 启用部门响应:', response)
    return response
  }

  /**
   * 禁用部门
   * @param id 部门ID
   * @returns 禁用结果
   */
  static async disableDept(id: number): Promise<void> {
    console.log('🚀 发送禁用部门请求:', id)
    const params = new URLSearchParams()
    params.append('status', '0')
    const response = await httpClient.put<void>(`/system/dept/${id}/status`, null, { params })
    console.log('✅ 禁用部门响应:', response)
    return response
  }

  /**
   * 检查部门编码是否存在
   * @param deptCode 部门编码
   * @param excludeId 排除的部门ID（编辑时使用）
   * @returns 是否存在
   */
  static async checkDeptCodeExists(deptCode: string, excludeId?: number): Promise<boolean> {
    console.log('🚀 发送检查部门编码请求:', { deptCode, excludeId })
    const params: any = { deptCode }
    if (excludeId) {
      params.excludeId = excludeId
    }
    const response = await httpClient.get<boolean>('/system/dept/check-code', { params })
    console.log('✅ 检查部门编码响应:', response)
    return response
  }

  /**
   * 检查部门名称是否存在（同级部门下）
   * @param deptName 部门名称
   * @param parentId 父部门ID
   * @param excludeId 排除的部门ID（编辑时使用）
   * @returns 是否存在
   */
  static async checkDeptNameExists(deptName: string, parentId: number, excludeId?: number): Promise<boolean> {
    console.log('🚀 发送检查部门名称请求:', { deptName, parentId, excludeId })
    const params: any = { deptName, parentId }
    if (excludeId) {
      params.excludeId = excludeId
    }
    const response = await httpClient.get<boolean>('/system/dept/check-name', { params })
    console.log('✅ 检查部门名称响应:', response)
    return response
  }

  /**
   * 分配用户到部门
   * @param userId 用户ID
   * @param deptIds 部门ID列表
   * @param primaryDeptId 主部门ID
   * @returns 分配结果
   */
  static async assignUserToDepts(userId: number, deptIds: number[], primaryDeptId?: number): Promise<void> {
    console.log('🚀 发送分配用户到部门请求:', { userId, deptIds, primaryDeptId })
    const params = new URLSearchParams()
    params.append('userId', userId.toString())
    deptIds.forEach(deptId => params.append('deptIds', deptId.toString()))
    if (primaryDeptId) {
      params.append('primaryDeptId', primaryDeptId.toString())
    }

    const response = await httpClient.post<void>('/system/dept/assign-user', null, { params })
    console.log('✅ 分配用户到部门响应:', response)
    return response
  }

  /**
   * 从部门中移除用户
   * @param userId 用户ID
   * @param deptId 部门ID
   * @returns 移除结果
   */
  static async removeUserFromDept(userId: number, deptId: number): Promise<void> {
    console.log('🚀 发送从部门移除用户请求:', { userId, deptId })
    const params = new URLSearchParams()
    params.append('userId', userId.toString())
    params.append('deptId', deptId.toString())

    const response = await httpClient.delete<void>('/system/dept/remove-user', { params })
    console.log('✅ 从部门移除用户响应:', response)
    return response
  }

  /**
   * 获取部门下的用户列表
   * @param deptId 部门ID
   * @returns 用户列表
   */
  static async getDeptUsers(deptId: number): Promise<any[]> {
    console.log('🚀 发送获取部门用户请求:', deptId)
    const response = await httpClient.get<any[]>(`/system/dept/${deptId}/users`)
    console.log('✅ 获取部门用户响应:', response)
    return response
  }

  /**
   * 获取用户的部门列表
   * @param userId 用户ID
   * @returns 部门列表
   */
  static async getUserDepts(userId: number): Promise<Dept[]> {
    console.log('🚀 发送获取用户部门请求:', userId)
    const response = await httpClient.get<Dept[]>(`/system/dept/user/${userId}`)
    console.log('✅ 获取用户部门响应:', response)
    return response
  }

  /**
   * 设置用户主部门
   * @param userId 用户ID
   * @param deptId 部门ID
   * @returns 设置结果
   */
  static async setUserPrimaryDept(userId: number, deptId: number): Promise<void> {
    console.log('🚀 发送设置用户主部门请求:', { userId, deptId })
    const params = new URLSearchParams()
    params.append('userId', userId.toString())
    params.append('deptId', deptId.toString())

    const response = await httpClient.put<void>('/system/dept/set-primary', null, { params })
    console.log('✅ 设置用户主部门响应:', response)
    return response
  }

  /**
   * 获取部门统计信息
   * @returns 统计信息
   */
  static async getDeptStats(): Promise<DeptStats> {
    console.log('🚀 发送获取部门统计请求')
    const response = await httpClient.get<DeptStats>('/system/dept/stats')
    console.log('✅ 获取部门统计响应:', response)
    return response
  }
}

export default DeptService
