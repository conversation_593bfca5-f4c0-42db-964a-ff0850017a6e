/**
 * 权限包装组件
 * 
 * 提供标准化的权限控制包装器，支持页面级和组件级权限控制
 * 统一重构版本 - 为所有模块提供一致的权限控制体验
 */

import React from 'react'
import { PermissionGuard } from '@/router/guards'
import { PermissionButton } from './PermissionButton'
import { usePermission } from '@/stores/auth'
import { 
  getModulePermissions, 
  getModulePermission,
  type PermissionAction,
  type PermissionButtonConfig,
  logPermissionDebug
} from '@/utils/permissionUtils'

/**
 * 页面权限包装器属性
 */
export interface PagePermissionWrapperProps {
  /** 模块名称 */
  module: string
  /** 子组件 */
  children: React.ReactNode
  /** 无权限时的自定义内容 */
  fallback?: React.ReactNode
  /** 是否启用调试模式 */
  debug?: boolean
}

/**
 * 页面权限包装器
 * 
 * 用于包装整个页面，提供页面级权限控制
 */
export const PagePermissionWrapper: React.FC<PagePermissionWrapperProps> = ({
  module,
  children,
  fallback,
  debug = false
}) => {
  const { hasAllPermissions, isSuperAdmin } = usePermission()
  const permissions = getModulePermissions(module)
  
  // 页面访问需要的权限（通常是 view 和 list 权限）
  const requiredPermissions = [permissions.view, permissions.list].filter(Boolean)
  
  // 权限检查结果
  const hasPermission = isSuperAdmin() || hasAllPermissions(requiredPermissions)
  const missingPermissions = requiredPermissions.filter(p => !hasAllPermissions([p]))
  
  // 调试信息
  if (debug) {
    logPermissionDebug(module, 'page-access', {
      hasPermission,
      missingPermissions,
      isSuperAdmin: isSuperAdmin()
    }, { requiredPermissions })
  }
  
  return (
    <PermissionGuard
      permissions={requiredPermissions}
      fallback={fallback || (
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold mb-2">访问受限</h2>
          <p className="text-muted-foreground mb-4">
            您没有权限访问此页面，请联系管理员获取相应权限。
          </p>
        </div>
      )}
    >
      {children}
    </PermissionGuard>
  )
}

/**
 * 操作权限按钮属性
 */
export interface ActionPermissionButtonProps {
  /** 模块名称 */
  module: string
  /** 操作类型 */
  action: PermissionAction
  /** 按钮配置 */
  config: Omit<PermissionButtonConfig, 'permissions'> & {
    /** 悬停提示文本 */
    title?: string
    /** 自定义样式类名 */
    className?: string
  }
  /** 是否启用调试模式 */
  debug?: boolean
}

/**
 * 操作权限按钮
 *
 * 基于模块和操作类型自动获取权限的按钮组件
 */
export const ActionPermissionButton: React.FC<ActionPermissionButtonProps> = ({
  module,
  action,
  config,
  debug = false
}) => {
  const { hasAllPermissions, isSuperAdmin } = usePermission()
  const permission = getModulePermission(module, action)

  // 权限检查结果
  const hasPermission = isSuperAdmin() || hasAllPermissions([permission])

  // 调试信息
  if (debug) {
    logPermissionDebug(module, action, {
      hasPermission,
      missingPermissions: hasPermission ? [] : [permission],
      isSuperAdmin: isSuperAdmin()
    }, { permission, config })
  }

  if (!permission) {
    console.warn(`模块 ${module} 的操作 ${action} 没有配置权限`)
    return null
  }

  // 判断是否为纯图标按钮（表格中使用）
  const isIconOnly = !config.text || config.text.trim() === ''

  return (
    <PermissionButton
      permissions={[permission]}
      noPermissionBehavior={config.noPermissionBehavior || 'hide'}
      variant={config.variant}
      size={config.size}
      disabled={config.disabled}
      onClick={config.onClick}
      title={config.title} // 悬停提示
      className={config.className} // 自定义样式
    >
      {config.icon && (
        <config.icon
          className={isIconOnly ? "w-4 h-4" : "w-4 h-4 mr-2"}
        />
      )}
      {config.text}
    </PermissionButton>
  )
}

/**
 * 批量操作权限按钮组属性
 */
export interface BatchActionButtonsProps {
  /** 模块名称 */
  module: string
  /** 按钮配置列表 */
  actions: Array<{
    action: PermissionAction
    config: Omit<PermissionButtonConfig, 'permissions'>
  }>
  /** 容器样式类名 */
  className?: string
  /** 是否启用调试模式 */
  debug?: boolean
}

/**
 * 批量操作权限按钮组
 * 
 * 用于渲染一组操作按钮，每个按钮都有对应的权限控制
 */
export const BatchActionButtons: React.FC<BatchActionButtonsProps> = ({
  module,
  actions,
  className = 'flex items-center gap-2',
  debug = false
}) => {
  return (
    <div className={className}>
      {actions.map(({ action, config }, index) => (
        <ActionPermissionButton
          key={`${action}-${index}`}
          module={module}
          action={action}
          config={config}
          debug={debug}
        />
      ))}
    </div>
  )
}

/**
 * 表格行操作权限按钮属性
 */
export interface TableRowActionsProps {
  /** 模块名称 */
  module: string
  /** 数据项 */
  item: any
  /** 操作配置 */
  actions: Array<{
    action: PermissionAction
    text: string
    icon?: React.ComponentType<any>
    variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
    onClick: (item: any) => void
    disabled?: (item: any) => boolean
  }>
  /** 是否启用调试模式 */
  debug?: boolean
}

/**
 * 表格行操作权限按钮
 *
 * 用于表格行中的操作按钮，支持基于数据项的动态权限控制
 * 表格中使用图标按钮，提供更简洁的界面
 */
export const TableRowActions: React.FC<TableRowActionsProps> = ({
  module,
  item,
  actions,
  debug = false
}) => {
  return (
    <div className="flex items-center gap-1">
      {actions.map(({ action, text, icon, variant = 'ghost', onClick, disabled }, index) => (
        <ActionPermissionButton
          key={`${action}-${index}`}
          module={module}
          action={action}
          config={{
            text: '', // 表格中不显示文字，只显示图标
            icon,
            variant,
            size: 'sm',
            onClick: () => onClick(item),
            disabled: disabled ? disabled(item) : false,
            noPermissionBehavior: 'hide',
            // 添加 title 属性用于悬停提示
            title: text
          }}
          debug={debug}
        />
      ))}
    </div>
  )
}

/**
 * 权限控制的搜索和操作栏
 */
export interface PermissionToolbarProps {
  /** 模块名称 */
  module: string
  /** 搜索组件 */
  searchComponent?: React.ReactNode
  /** 主要操作按钮配置 */
  primaryActions?: Array<{
    action: PermissionAction
    config: Omit<PermissionButtonConfig, 'permissions'>
  }>
  /** 次要操作按钮配置 */
  secondaryActions?: Array<{
    action: PermissionAction
    config: Omit<PermissionButtonConfig, 'permissions'>
  }>
  /** 自定义操作组件 */
  customActions?: React.ReactNode
  /** 是否启用调试模式 */
  debug?: boolean
}

/**
 * 权限控制的工具栏组件
 * 
 * 提供标准化的页面工具栏，包含搜索和操作按钮
 */
export const PermissionToolbar: React.FC<PermissionToolbarProps> = ({
  module,
  searchComponent,
  primaryActions = [],
  secondaryActions = [],
  customActions,
  debug = false
}) => {
  return (
    <div className="flex items-center justify-between gap-4 mb-6">
      {/* 搜索区域 */}
      <div className="flex-1">
        {searchComponent}
      </div>
      
      {/* 操作按钮区域 */}
      <div className="flex items-center gap-2">
        {/* 次要操作按钮 */}
        {secondaryActions.length > 0 && (
          <BatchActionButtons
            module={module}
            actions={secondaryActions}
            debug={debug}
          />
        )}
        
        {/* 自定义操作 */}
        {customActions}
        
        {/* 主要操作按钮 */}
        {primaryActions.length > 0 && (
          <BatchActionButtons
            module={module}
            actions={primaryActions}
            debug={debug}
          />
        )}
      </div>
    </div>
  )
}
