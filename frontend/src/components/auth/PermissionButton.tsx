/**
 * 权限按钮组件
 * 
 * 基于用户权限控制按钮的显示和可用性
 * 支持权限检查、角色检查和自定义fallback
 */

import React from 'react'
import { Button, type ButtonProps } from '@/components/ui'
import { usePermission } from '@/stores/auth'
import { PermissionGuard } from '@/router/guards'

export interface PermissionButtonProps extends ButtonProps {
  /**
   * 需要的权限列表（需要全部满足）
   */
  permissions?: string[]
  
  /**
   * 需要的角色列表（满足任一即可）
   */
  roles?: string[]
  
  /**
   * 权限检查模式
   * - 'all': 需要拥有所有指定权限（默认）
   * - 'any': 拥有任一权限即可
   */
  permissionMode?: 'all' | 'any'
  
  /**
   * 无权限时的行为
   * - 'hide': 隐藏按钮（默认）
   * - 'disable': 禁用按钮
   * - 'show': 显示按钮但可能有视觉提示
   */
  noPermissionBehavior?: 'hide' | 'disable' | 'show'
  
  /**
   * 无权限时的提示文本
   */
  noPermissionTooltip?: string
  
  /**
   * 无权限时的自定义渲染内容
   */
  noPermissionFallback?: React.ReactNode
  
  /**
   * 是否显示权限调试信息（开发环境）
   */
  debug?: boolean
}

/**
 * 权限按钮组件
 */
export const PermissionButton: React.FC<PermissionButtonProps> = ({
  permissions = [],
  roles = [],
  permissionMode = 'all',
  noPermissionBehavior = 'hide',
  noPermissionTooltip,
  noPermissionFallback,
  debug = false,
  children,
  disabled,
  title,
  ...buttonProps
}) => {
  const { hasAllPermissions, hasAnyPermission, hasAnyRole, isSuperAdmin, permissions: userPermissions, roles: userRoles } = usePermission()

  // 权限检查
  const checkPermissions = (): boolean => {
    // 超级管理员拥有所有权限
    if (isSuperAdmin()) {
      return true
    }

    // 检查权限
    let hasRequiredPermissions = true
    if (permissions.length > 0) {
      if (permissionMode === 'all') {
        hasRequiredPermissions = hasAllPermissions(permissions)
      } else {
        hasRequiredPermissions = hasAnyPermission(permissions)
      }
    }

    // 检查角色
    let hasRequiredRoles = true
    if (roles.length > 0) {
      hasRequiredRoles = hasAnyRole(roles)
    }

    return hasRequiredPermissions && hasRequiredRoles
  }

  const hasAccess = checkPermissions()

  // 开发环境调试信息
  if (debug && process.env.NODE_ENV === 'development') {
    console.log('PermissionButton Debug:', {
      permissions,
      roles,
      permissionMode,
      hasAccess,
      isSuperAdmin: isSuperAdmin(),
      userPermissions,
      userRoles
    })
  }

  // 根据权限检查结果和行为配置决定渲染方式
  if (!hasAccess) {
    switch (noPermissionBehavior) {
      case 'hide':
        return noPermissionFallback || null
        
      case 'disable':
        return (
          <Button
            {...buttonProps}
            disabled={true}
            title={noPermissionTooltip || title || '权限不足'}
          >
            {children}
          </Button>
        )
        
      case 'show':
        return (
          <Button
            {...buttonProps}
            disabled={disabled}
            title={noPermissionTooltip || title}
            className={`${buttonProps.className || ''} opacity-60`}
          >
            {children}
          </Button>
        )
        
      default:
        return null
    }
  }

  // 有权限，正常渲染按钮
  return (
    <Button
      {...buttonProps}
      disabled={disabled}
      title={title}
    >
      {children}
    </Button>
  )
}

/**
 * 权限按钮组（批量权限控制）
 */
export interface PermissionButtonGroupProps {
  /**
   * 需要的权限列表
   */
  permissions?: string[]
  
  /**
   * 需要的角色列表
   */
  roles?: string[]
  
  /**
   * 无权限时的行为
   */
  noPermissionBehavior?: 'hide' | 'disable' | 'show'
  
  /**
   * 无权限时的自定义渲染内容
   */
  noPermissionFallback?: React.ReactNode
  
  /**
   * 子组件
   */
  children: React.ReactNode
  
  /**
   * 容器样式类名
   */
  className?: string
}

export const PermissionButtonGroup: React.FC<PermissionButtonGroupProps> = ({
  permissions = [],
  roles = [],
  noPermissionFallback,
  children,
  className
}) => {
  return (
    <PermissionGuard
      permissions={permissions}
      roles={roles}
      fallback={noPermissionFallback}
    >
      <div className={className}>
        {children}
      </div>
    </PermissionGuard>
  )
}

/**
 * 权限链接组件
 */
export interface PermissionLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  permissions?: string[]
  roles?: string[]
  noPermissionBehavior?: 'hide' | 'disable' | 'show'
  noPermissionFallback?: React.ReactNode
}

export const PermissionLink: React.FC<PermissionLinkProps> = ({
  permissions = [],
  roles = [],
  noPermissionBehavior = 'hide',
  noPermissionFallback,
  children,
  className,
  ...linkProps
}) => {
  const { hasAllPermissions, hasAnyRole, isSuperAdmin } = usePermission()

  // 权限检查
  const hasAccess = isSuperAdmin() || 
    (permissions.length === 0 || hasAllPermissions(permissions)) &&
    (roles.length === 0 || hasAnyRole(roles))

  if (!hasAccess) {
    switch (noPermissionBehavior) {
      case 'hide':
        return noPermissionFallback || null
        
      case 'disable':
        return (
          <span className={`${className || ''} opacity-50 cursor-not-allowed`}>
            {children}
          </span>
        )
        
      case 'show':
        return (
          <span className={`${className || ''} opacity-60`}>
            {children}
          </span>
        )
        
      default:
        return null
    }
  }

  return (
    <a {...linkProps} className={className}>
      {children}
    </a>
  )
}

export default PermissionButton
