/**
 * 菜单权限树组件
 * 用于角色管理中的菜单权限分配
 */

import React, { useState, useEffect, useMemo } from 'react'
import { Tree, Checkbox, Input, Button, Space, Alert, Spin } from 'antd'
import { SearchOutlined, ReloadOutlined, ExpandAltOutlined, CompressOutlined } from '@ant-design/icons'
import type { TreeProps, TreeDataNode } from 'antd'
import type { Menu } from '@/types/api'
import { MenuService } from '@/services/menu'
import { MENU_TYPE, getMenuTypeLabel, getMenuTypeIcon, getMenuTypeColor, isPermissionMenuType } from '@/constants/menuPermission'

interface MenuPermissionTreeProps {
  /** 已选中的菜单ID列表 */
  selectedMenuIds?: number[]
  /** 选择变化回调 */
  onSelectionChange?: (selectedMenuIds: number[], selectedPermissionCodes: string[]) => void
  /** 是否只显示权限菜单 */
  permissionOnly?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 高度 */
  height?: number
}

interface TreeNode extends TreeDataNode {
  id: number
  menuType: number
  permissionCode?: string
  children?: TreeNode[]
}

const MenuPermissionTree: React.FC<MenuPermissionTreeProps> = ({
  selectedMenuIds = [],
  onSelectionChange,
  permissionOnly = false,
  disabled = false,
  height = 400
}) => {
  const [loading, setLoading] = useState(false)
  const [menuData, setMenuData] = useState<Menu[]>([])
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([])
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([])
  const [searchValue, setSearchValue] = useState('')
  const [autoExpandParent, setAutoExpandParent] = useState(true)

  // 加载菜单数据
  const loadMenuData = async () => {
    try {
      setLoading(true)
      const data = permissionOnly 
        ? await MenuService.getPermissionMenuTree()
        : await MenuService.getMenuTree()
      setMenuData(data)
      
      // 默认展开第一级
      const firstLevelKeys = data.map(item => item.id.toString())
      setExpandedKeys(firstLevelKeys)
    } catch (error) {
      console.error('加载菜单数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 初始化加载
  useEffect(() => {
    loadMenuData()
  }, [permissionOnly])

  // 同步选中状态
  useEffect(() => {
    setCheckedKeys(selectedMenuIds.map(id => id.toString()))
  }, [selectedMenuIds])

  // 转换菜单数据为树节点
  const treeData = useMemo(() => {
    const convertToTreeNode = (menus: Menu[]): TreeNode[] => {
      return menus.map(menu => {
        const isPermission = isPermissionMenuType(menu.type)
        
        return {
          key: menu.id.toString(),
          id: menu.id,
          menuType: menu.type,
          permissionCode: menu.permissionCode,
          title: (
            <div className="flex items-center space-x-2">
              <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-${getMenuTypeColor(menu.type)}-100 text-${getMenuTypeColor(menu.type)}-800`}>
                {getMenuTypeLabel(menu.type)}
              </span>
              <span className="font-medium">{menu.title}</span>
              {menu.permissionCode && (
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  {menu.permissionCode}
                </span>
              )}
              {isPermission && (
                <span className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">
                  权限点
                </span>
              )}
            </div>
          ),
          children: menu.children ? convertToTreeNode(menu.children) : undefined,
          disabled: disabled || (!isPermission && permissionOnly)
        }
      })
    }

    return convertToTreeNode(menuData)
  }, [menuData, disabled, permissionOnly])

  // 搜索过滤
  const filteredTreeData = useMemo(() => {
    if (!searchValue) return treeData

    const filterTree = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.reduce((acc: TreeNode[], node) => {
        const matchesSearch = node.title?.toString().toLowerCase().includes(searchValue.toLowerCase()) ||
                             node.permissionCode?.toLowerCase().includes(searchValue.toLowerCase())
        
        const filteredChildren = node.children ? filterTree(node.children) : []
        
        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...node,
            children: filteredChildren.length > 0 ? filteredChildren : undefined
          })
        }
        
        return acc
      }, [])
    }

    return filterTree(treeData)
  }, [treeData, searchValue])

  // 处理选择变化
  const handleCheck: TreeProps['onCheck'] = (checkedKeysValue, info) => {
    const keys = Array.isArray(checkedKeysValue) ? checkedKeysValue : checkedKeysValue.checked
    setCheckedKeys(keys)

    // 获取选中的菜单ID和权限编码
    const selectedIds = keys.map(key => parseInt(key.toString()))
    const selectedPermissions: string[] = []

    const collectPermissions = (nodes: TreeNode[]) => {
      nodes.forEach(node => {
        if (keys.includes(node.key) && node.permissionCode) {
          selectedPermissions.push(node.permissionCode)
        }
        if (node.children) {
          collectPermissions(node.children)
        }
      })
    }

    collectPermissions(treeData)

    onSelectionChange?.(selectedIds, selectedPermissions)
  }

  // 处理展开/收起
  const handleExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue)
    setAutoExpandParent(false)
  }

  // 全部展开
  const expandAll = () => {
    const getAllKeys = (nodes: TreeNode[]): string[] => {
      let keys: string[] = []
      nodes.forEach(node => {
        keys.push(node.key.toString())
        if (node.children) {
          keys = keys.concat(getAllKeys(node.children))
        }
      })
      return keys
    }
    setExpandedKeys(getAllKeys(treeData))
  }

  // 全部收起
  const collapseAll = () => {
    setExpandedKeys([])
  }

  // 全选/反选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const getAllKeys = (nodes: TreeNode[]): string[] => {
        let keys: string[] = []
        nodes.forEach(node => {
          if (!node.disabled) {
            keys.push(node.key.toString())
          }
          if (node.children) {
            keys = keys.concat(getAllKeys(node.children))
          }
        })
        return keys
      }
      const allKeys = getAllKeys(treeData)
      setCheckedKeys(allKeys)
      
      const selectedIds = allKeys.map(key => parseInt(key))
      const selectedPermissions: string[] = []
      
      const collectPermissions = (nodes: TreeNode[]) => {
        nodes.forEach(node => {
          if (allKeys.includes(node.key.toString()) && node.permissionCode) {
            selectedPermissions.push(node.permissionCode)
          }
          if (node.children) {
            collectPermissions(node.children)
          }
        })
      }
      
      collectPermissions(treeData)
      onSelectionChange?.(selectedIds, selectedPermissions)
    } else {
      setCheckedKeys([])
      onSelectionChange?.([], [])
    }
  }

  const isAllSelected = checkedKeys.length > 0 && checkedKeys.length === treeData.length
  const isIndeterminate = checkedKeys.length > 0 && checkedKeys.length < treeData.length

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <Space>
          <Input
            placeholder="搜索菜单或权限编码"
            prefix={<SearchOutlined />}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            style={{ width: 200 }}
            allowClear
          />
          <Button
            icon={<ReloadOutlined />}
            onClick={loadMenuData}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
        
        <Space>
          <Checkbox
            indeterminate={isIndeterminate}
            checked={isAllSelected}
            onChange={(e) => handleSelectAll(e.target.checked)}
            disabled={disabled}
          >
            全选
          </Checkbox>
          <Button
            icon={<ExpandAltOutlined />}
            size="small"
            onClick={expandAll}
          >
            展开
          </Button>
          <Button
            icon={<CompressOutlined />}
            size="small"
            onClick={collapseAll}
          >
            收起
          </Button>
        </Space>
      </div>

      {/* 提示信息 */}
      {permissionOnly && (
        <Alert
          message="权限分配模式"
          description="当前只显示按钮类型的权限点，用于精确的权限控制。"
          type="info"
          showIcon
          closable
        />
      )}

      {/* 统计信息 */}
      <div className="text-sm text-gray-600">
        已选择 {checkedKeys.length} 项
        {permissionOnly && (
          <span className="ml-2">
            (权限点: {checkedKeys.filter(key => {
              const findNode = (nodes: TreeNode[]): TreeNode | undefined => {
                for (const node of nodes) {
                  if (node.key === key) return node
                  if (node.children) {
                    const found = findNode(node.children)
                    if (found) return found
                  }
                }
                return undefined
              }
              const node = findNode(treeData)
              return node && isPermissionMenuType(node.menuType)
            }).length})
          </span>
        )}
      </div>

      {/* 树形组件 */}
      <div style={{ height, overflow: 'auto' }} className="border rounded">
        <Spin spinning={loading}>
          <Tree
            checkable
            onExpand={handleExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onCheck={handleCheck}
            checkedKeys={checkedKeys}
            treeData={filteredTreeData}
            disabled={disabled}
            showLine
            showIcon={false}
          />
        </Spin>
      </div>
    </div>
  )
}

export default MenuPermissionTree
