/**
 * 运营统计卡片组件
 * 
 * 显示总主播数、活跃主播数、总用户数等关键指标
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui'
import { Users, UserCheck, UsersRound, TrendingUp, DollarSign, Activity } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface OperationsStats {
  totalAnchors: number
  activeAnchors: number
  totalUsers: number
  newAnchorsThisMonth?: number
  newUsersThisMonth?: number
  totalRechargeThisMonth?: number
  totalConsumeThisMonth?: number
  statsTime?: string
}

export interface StatsCardsProps {
  stats: OperationsStats | null
  loading?: boolean
  error?: string | null
  className?: string
}

/**
 * 格式化数字显示
 */
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

/**
 * 格式化金额显示
 */
const formatAmount = (amount: number): string => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + '万'
  }
  return amount.toLocaleString()
}

/**
 * 统计卡片项组件
 */
const StatsCard: React.FC<{
  title: string
  value: string | number
  description: string
  icon: React.ReactNode
  trend?: {
    value: number
    label: string
  }
  loading?: boolean
  className?: string
}> = ({ title, value, description, icon, trend, loading, className }) => {
  return (
    <Card className={cn('transition-all duration-200 hover:shadow-md', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {loading ? (
            <div className="h-8 w-16 bg-muted animate-pulse rounded" />
          ) : (
            value
          )}
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          {description}
        </p>
        {trend && !loading && (
          <div className="flex items-center mt-2 text-xs">
            <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
            <span className="text-green-500 font-medium">+{trend.value}</span>
            <span className="text-muted-foreground ml-1">{trend.label}</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 运营统计卡片组件
 */
export const StatsCards: React.FC<StatsCardsProps> = ({
  stats,
  loading = false,
  error,
  className
}) => {
  // 错误状态
  if (error) {
    return (
      <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}>
        <Card className="col-span-full">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <Activity className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">加载统计数据失败</p>
              <p className="text-xs text-muted-foreground mt-1">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}>
      {/* 总主播数 */}
      <StatsCard
        title="总主播数"
        value={loading ? '' : formatNumber(stats?.totalAnchors || 0)}
        description="线上/线下主播总数"
        icon={<Users className="h-4 w-4" />}
        trend={stats?.newAnchorsThisMonth ? {
          value: stats.newAnchorsThisMonth,
          label: '本月新增'
        } : undefined}
        loading={loading}
      />

      {/* 活跃主播 */}
      <StatsCard
        title="活跃主播"
        value={loading ? '' : formatNumber(stats?.activeAnchors || 0)}
        description="本月有业务的主播数"
        icon={<UserCheck className="h-4 w-4" />}
        loading={loading}
        className="border-orange-200 bg-orange-50/50"
      />

      {/* 总用户数 */}
      <StatsCard
        title="总用户数"
        value={loading ? '' : formatNumber(stats?.totalUsers || 0)}
        description="所有主播的下级用户总数"
        icon={<UsersRound className="h-4 w-4" />}
        trend={stats?.newUsersThisMonth ? {
          value: stats.newUsersThisMonth,
          label: '本月新增'
        } : undefined}
        loading={loading}
        className="border-blue-200 bg-blue-50/50"
      />

      {/* 本月充值金额 */}
      {stats?.totalRechargeThisMonth !== undefined && (
        <StatsCard
          title="本月充值"
          value={loading ? '' : `¥${formatAmount(stats.totalRechargeThisMonth)}`}
          description="本月总充值金额"
          icon={<DollarSign className="h-4 w-4" />}
          loading={loading}
          className="border-green-200 bg-green-50/50"
        />
      )}

      {/* 本月消费金额 */}
      {/*{stats?.totalConsumeThisMonth !== undefined && (*/}
      {/*  <StatsCard*/}
      {/*    title="本月消费"*/}
      {/*    value={loading ? '' : `¥${formatAmount(stats.totalConsumeThisMonth)}`}*/}
      {/*    description="本月总消费金额"*/}
      {/*    icon={<Activity className="h-4 w-4" />}*/}
      {/*    loading={loading}*/}
      {/*    className="border-purple-200 bg-purple-50/50"*/}
      {/*  />*/}
      {/*)}*/}
    </div>
  )
}

export default StatsCards
