import { useState } from "react"
import { addDays, format, startOfMonth, endOfMonth, subMonths, startOfDay, endOfDay } from "date-fns"
import { CalendarIcon, ChevronDown } from "lucide-react"
import type { DateRange } from "react-day-picker"

import { cn } from "@/utils"
import { Button } from "./Button"
import { Calendar } from "./calendar"
import { Popover, PopoverContent, PopoverTrigger } from "./popover"
import { Card } from "./Card"
import { Separator } from "./separator"

interface DateRangePickerProps {
    className?: string
    onDateChange?: (range: DateRange | undefined) => void
}

const quickOptions = [
    {
        label: "今天",
        getValue: () => ({
            from: startOfDay(new Date()),
            to: endOfDay(new Date()),
        }),
    },
    {
        label: "昨天",
        getValue: () => ({
            from: startOfDay(addDays(new Date(), -1)),
            to: endOfDay(addDays(new Date(), -1)),
        }),
    },
    {
        label: "过去7天",
        getValue: () => ({
            from: startOfDay(addDays(new Date(), -6)),
            to: endOfDay(new Date()),
        }),
    },
    {
        label: "过去30天",
        getValue: () => ({
            from: startOfDay(addDays(new Date(), -29)),
            to: endOfDay(new Date()),
        }),
    },
    {
        label: "本月",
        getValue: () => ({
            from: startOfMonth(new Date()),
            to: endOfMonth(new Date()),
        }),
    },
    {
        label: "上个月",
        getValue: () => {
            const lastMonth = subMonths(new Date(), 1)
            return {
                from: startOfMonth(lastMonth),
                to: endOfMonth(lastMonth),
            }
        },
    },
]

export default function DateRangePicker({ className, onDateChange }: DateRangePickerProps) {
    const [date, setDate] = useState<DateRange | undefined>({
        from: startOfDay(addDays(new Date(), -7)),
        to: endOfDay(new Date()),
    })
    const [open, setOpen] = useState(false)

    const handleDateChange = (newDate: DateRange | undefined) => {
        setDate(newDate)
        onDateChange?.(newDate)
    }

    const handleQuickSelect = (range: DateRange) => {
        handleDateChange(range)
        setOpen(false)
    }

    const formatDateRange = (range: DateRange | undefined) => {
        if (!range?.from) return "选择日期范围"

        if (!range.to) {
            return format(range.from, "yyyy年MM月dd日")
        }

        if (range.from.toDateString() === range.to.toDateString()) {
            return format(range.from, "yyyy年MM月dd日")
        }

        return `${format(range.from, "yyyy年MM月dd日")} - ${format(range.to, "yyyy年MM月dd日")}`
    }
    return (
        <div className={cn("grid gap-2", className)}>
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <Button
                        id="date"
                        variant="outline"
                        className={cn("w-full justify-start text-left font-normal h-11 px-4", !date && "text-muted-foreground")}
                    >
                        <CalendarIcon className="mr-3 h-4 w-4 opacity-70" />
                        <span className="flex-1 truncate">{formatDateRange(date)}</span>
                        <ChevronDown className="ml-2 h-4 w-4 opacity-50" />
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                    <div className="flex">
                        {/* 快捷选项侧边栏 */}
                        <Card className="w-48 p-3 border-r rounded-r-none">
                            <div className="space-y-1">
                                <h4 className="text-sm font-medium text-muted-foreground mb-3">快捷选择</h4>
                                {quickOptions.map((option) => (
                                    <Button
                                        key={option.label}
                                        variant="ghost"
                                        size="sm"
                                        className="w-full justify-start h-8 px-2 text-sm font-normal hover:bg-accent"
                                        onClick={() => handleQuickSelect(option.getValue())}
                                    >
                                        {option.label}
                                    </Button>
                                ))}
                            </div>
                        </Card>

                        <Separator orientation="vertical" className="h-auto" />
                        {/* 日历区域 */}
                        <div className="p-3">
                            <Calendar
                                autoFocus
                                mode="range"
                                defaultMonth={date?.from}
                                selected={date}
                                onSelect={handleDateChange}
                                numberOfMonths={2}
                                className="rounded-md"
                                classNames={{
                                    months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                                    month: "space-y-4",
                                    caption: "flex justify-center pt-1 relative items-center",
                                    caption_label: "text-sm font-medium",
                                    nav: "space-x-1 flex items-center",
                                    nav_button: cn("h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),
                                    nav_button_previous: "absolute left-1",
                                    nav_button_next: "absolute right-1",
                                    table: "w-full border-collapse space-y-1",
                                    head_row: "flex",
                                    head_cell: "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
                                    row: "flex w-full mt-2",
                                    cell: cn(
                                        "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-md",
                                        "[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md",
                                    ),
                                    day: cn(
                                        "h-8 w-8 p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-md",
                                    ),
                                    day_range_start: "day-range-start",
                                    day_range_end: "day-range-end",
                                    day_selected:
                                        "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                                    day_today: "bg-accent text-accent-foreground font-semibold",
                                    day_outside:
                                        "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
                                    day_disabled: "text-muted-foreground opacity-50",
                                    day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                                    day_hidden: "invisible",
                                }}
                            />

                            <Separator className="my-4" />

                            <div className="flex justify-between items-center">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        handleDateChange(undefined)
                                        setOpen(false)
                                    }}
                                >
                                    清除
                                </Button>
                                <Button
                                    size="sm"
                                    onClick={() => {
                                        if (date?.from && date?.to) {
                                            handleDateChange(date)
                                        }
                                        setOpen(false)
                                    }}
                                    disabled={!date?.from || !date?.to}
                                >
                                    确认选择
                                </Button>
                            </div>
                        </div>
                    </div>
                </PopoverContent>
            </Popover>
        </div>
    )
}