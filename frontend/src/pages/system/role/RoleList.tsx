import React, { useState, useEffect, useCallback } from 'react'
import { User<PERSON>heck, Plus, Trash2, Power, PowerOff, Download, Upload, RefreshCw, Search } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle, Button, ConfirmDialog } from '../../../components/ui'
import { usePageTitle } from '../../../router/guards.tsx'
import { PagePermissionWrapper, PermissionToolbar } from '../../../components/auth/PermissionWrapper'
import RoleTable from './components/RoleTable.tsx'
import RoleSearchForm from './components/RoleSearchForm.tsx'
import RoleForm from './components/RoleForm.tsx'
import MenuAssignDialog from './components/MenuAssignDialog'
import type { Role, RoleQueryRequest } from '../../../types/role'
import { RoleService } from '../../../services/role'
import { RoleStatus } from '../../../types/role'

/**
 * 角色列表页面组件
 */
const RoleList: React.FC = () => {
  // 设置页面标题
  usePageTitle('角色管理')

  // 状态管理
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedIds, setSelectedIds] = useState<number[]>([])
  const [searchParams, setSearchParams] = useState<RoleQueryRequest>({
    pageNum: 1,
    pageSize: 10
  })
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 表单相关状态
  const [formOpen, setFormOpen] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)

  // 菜单分配对话框状态
  const [menuAssignDialogOpen, setMenuAssignDialogOpen] = useState(false)
  const [assigningRole, setAssigningRole] = useState<Role | null>(null)

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean
    title: string
    description: string
    onConfirm: () => void
  }>({
    open: false,
    title: '',
    description: '',
    onConfirm: () => {}
  })

  // 获取搜索关键词（用于高亮显示）
  const getSearchKeyword = useCallback(() => {
    return searchParams.roleCode || searchParams.roleName || ''
  }, [searchParams.roleCode, searchParams.roleName])

  // 加载角色数据
  const loadRoles = useCallback(async () => {
    try {
      setLoading(true)
      const result = await RoleService.pageRoles(searchParams)
      setRoles(result.records)
      setPagination({
        current: result.current,
        pageSize: result.size,
        total: result.total
      })
    } catch (error) {
      console.error('加载角色列表失败:', error)
      setRoles([])
      setPagination({
        current: 1,
        pageSize: 10,
        total: 0
      })
    } finally {
      setLoading(false)
    }
  }, [searchParams])

  // 初始化加载数据
  useEffect(() => {
    loadRoles()
  }, [loadRoles])

  // 监听搜索参数变化，重新加载数据
  useEffect(() => {
    loadRoles()
  }, [searchParams, loadRoles])

  // 处理搜索参数变更
  const handleSearchChange = (params: RoleQueryRequest) => {
    setSearchParams(params)
  }

  // 处理搜索提交
  const handleSearch = () => {
    // 重置到第一页
    const newParams = { ...searchParams, page: 1 }
    setSearchParams(newParams)
  }

  // 处理搜索重置
  const handleReset = () => {
    const resetParams: RoleQueryRequest = {
      page: 1,
      size: searchParams.size || 10
    }
    setSearchParams(resetParams)
  }

  // 处理分页变更
  const handlePaginationChange = (page: number, pageSize: number) => {
    const newParams = {
      ...searchParams,
      pageNum: page,
      pageSize: pageSize
    }
    setSearchParams(newParams)
  }

  // 处理排序变更
  const handleSortChange = (sortKey: keyof Role | null, direction: 'asc' | 'desc') => {
    setSearchParams({
      ...searchParams,
      sort: sortKey || undefined,
      order: sortKey ? direction : undefined,
      page: 1 // 排序时重置到第一页
    })
  }

  // 处理角色编辑
  const handleEdit = (role: Role) => {
    setEditingRole(role)
    setFormOpen(true)
  }

  // 处理角色删除
  const handleDelete = (role: Role) => {

    // 系统角色不允许删除
    if (role.roleType === 'SYSTEM') {
      console.warn('系统角色不允许删除')
      return
    }

    // 显示确认对话框
    setConfirmDialog({
      open: true,
      title: '删除角色',
      description: `确定要删除角色"${role.roleName}"吗？此操作不可恢复，该角色下的所有用户将失去相应权限。`,
      onConfirm: async () => {
        try {
          await RoleService.deleteRole(role.id)

          // 重新加载数据
          loadRoles()

          // 清空选择
          setSelectedIds([])
        } catch (error) {
          console.error('❌ 删除角色失败:', error)
          // TODO: 使用Toast组件显示错误信息
          alert('删除角色失败: ' + (error as Error).message)
        }
      }
    })
  }

  // 处理权限配置 - 统一使用菜单权限分配
  const handlePermissionConfig = (role: Role) => {
    // 直接打开菜单权限分配对话框，统一管理菜单和权限
    setAssigningRole(role)
    setMenuAssignDialogOpen(true)
  }

  // 处理菜单分配
  const handleMenuAssign = (role: Role) => {
    setAssigningRole(role)
    setMenuAssignDialogOpen(true)
  }

  // 菜单分配成功回调
  const handleMenuAssignSuccess = () => {
    // 可以在这里刷新数据或显示成功提示
    console.log('菜单分配成功')
  }

  // 获取数据权限范围标签
  const getDataScopeLabel = (dataScope: string) => {
    const labels: Record<string, string> = {
      'ALL': '全部数据权限',
      'DEPT': '部门数据权限',
      'DEPT_AND_SUB': '部门及子部门数据权限',
      'SELF': '仅本人数据权限',
      'CUSTOM': '自定义数据权限'
    }
    return labels[dataScope] || dataScope
  }

  // 处理状态切换
  const handleStatusToggle = async (role: Role) => {
    try {
      await RoleService.toggleRoleStatus(role.id, role.status)
      // 重新加载数据
      loadRoles()
    } catch (error) {
      console.error('切换角色状态失败:', error)
    }
  }

  // 处理新增角色
  const handleAdd = () => {
    setEditingRole(null)
    setFormOpen(true)
  }

  // 处理表单成功
  const handleFormSuccess = () => {
    loadRoles() // 重新加载数据
    setSelectedIds([]) // 清空选择
  }

  // 处理批量删除
  const handleBatchDelete = async () => {
    if (selectedIds.length === 0) {
      console.warn('请选择要删除的角色')
      return
    }

    try {
      setLoading(true)
      await RoleService.batchDeleteRoles(selectedIds)
      setSelectedIds([])
      loadRoles()
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }

  // 处理批量启用
  const handleBatchEnable = async () => {
    if (selectedIds.length === 0) {
      console.warn('请选择要启用的角色')
      return
    }

    try {
      setLoading(true)
      await RoleService.batchUpdateRoleStatus(selectedIds, RoleStatus.ENABLED)
      setSelectedIds([])
      loadRoles()
    } catch (error) {
      console.error('批量启用失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 处理批量禁用
  const handleBatchDisable = async () => {
    if (selectedIds.length === 0) {
      console.warn('请选择要禁用的角色')
      return
    }

    try {
      setLoading(true)
      await RoleService.batchUpdateRoleStatus(selectedIds, RoleStatus.DISABLED)
      setSelectedIds([])
      loadRoles()
    } catch (error) {
      console.error('批量禁用失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 处理导入
  const handleImport = () => {
    console.log('📥 导入角色')
    // TODO: 实现角色导入功能
  }

  // 处理导出
  const handleExport = async () => {
    try {
      setLoading(true)
      const blob = await RoleService.exportRoles(searchParams)

      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `角色列表_${new Date().toISOString().slice(0, 10)}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

    } catch (error) {
      console.error('导出失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <PagePermissionWrapper module="role">
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">角色管理</h1>
          <p className="text-muted-foreground">
            管理系统角色，包括角色的创建、编辑、删除和权限分配。
          </p>
        </div>

        {/* 权限控制的工具栏 */}
        <PermissionToolbar
          module="role"
          searchComponent={
            <RoleSearchForm
              searchParams={searchParams}
              onSearchChange={handleSearchChange}
              onSearch={handleSearch}
              onReset={handleReset}
              loading={loading}
            />
          }
          primaryActions={[
            {
              action: 'add',
              config: {
                text: '新增角色',
                icon: Plus,
                onClick: handleAdd,
                disabled: loading,
              }
            }
          ]}
          secondaryActions={[
            {
              action: 'export',
              config: {
                text: '导出',
                icon: Download,
                variant: 'outline',
                onClick: handleExport,
                disabled: loading,
              }
            }
          ]}
        />

      {/* 角色表格 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <UserCheck className="w-5 h-5 mr-2" />
              角色列表
              {pagination.total > 0 && (
                <span className="ml-2 text-sm font-normal text-muted-foreground">
                  (共 {pagination.total} 条)
                </span>
              )}
            </CardTitle>
            
            {/* 批量操作按钮 */}
            {selectedIds.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  已选择 {selectedIds.length} 项
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBatchEnable}
                  disabled={loading}
                >
                  <Power className="w-4 h-4 mr-1" />
                  批量启用
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBatchDisable}
                  disabled={loading}
                >
                  <PowerOff className="w-4 h-4 mr-1" />
                  批量禁用
                </Button>
                
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={loading}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      批量删除
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>确认删除</AlertDialogTitle>
                      <AlertDialogDescription>
                        您确定要删除选中的 {selectedIds.length} 个角色吗？此操作不可撤销。
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>取消</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleBatchDelete}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        确认删除
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            )}
            
            {/* 导出按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              disabled={loading}
            >
              <Download className="w-4 h-4 mr-1" />
              导出
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <RoleTable
            data={roles}
            loading={loading}
            selectedIds={selectedIds}
            onSelectionChange={setSelectedIds}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onMenuAssign={handleMenuAssign}
            onStatusToggle={handleStatusToggle}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: handlePaginationChange
            }}
            onSortChange={handleSortChange}
            searchKeyword={getSearchKeyword()}
          />
        </CardContent>
      </Card>

      {/* 角色表单对话框 */}
      <RoleForm
        open={formOpen}
        onOpenChange={setFormOpen}
        role={editingRole}
        onSuccess={handleFormSuccess}
      />

      {/* 菜单分配对话框 */}
      <MenuAssignDialog
        open={menuAssignDialogOpen}
        onClose={() => setMenuAssignDialogOpen(false)}
        role={assigningRole}
        onSuccess={handleMenuAssignSuccess}
      />

      {/* 确认对话框 */}
      <ConfirmDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}
        title={confirmDialog.title}
        description={confirmDialog.description}
        variant="destructive"
        confirmText="确认"
        cancelText="取消"
        onConfirm={confirmDialog.onConfirm}
      />
      </div>
    </PagePermissionWrapper>
  )
}

export default RoleList
