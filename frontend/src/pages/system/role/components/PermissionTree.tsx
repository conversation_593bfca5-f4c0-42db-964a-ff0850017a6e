/**
 * 权限树组件
 * 
 * 用于角色权限分配的树形选择组件
 * 支持父子节点联动、全选/反选等功能
 */

import React, {useMemo, useState} from 'react'
import {ChevronDown, ChevronRight} from 'lucide-react'
import {Button, Checkbox, Input} from '@/components/ui'
import {cn} from '@/lib/utils'
import type {Permission} from '@/types/api'


export interface PermissionTreeProps {
  permissions: Permission[]
  selectedIds: number[]
  onSelectionChange: (selectedIds: number[]) => void
  className?: string
}

interface TreeNode extends Permission {
  children: TreeNode[]
  level: number
}

/**
 * 获取模块名称
 */
const getModuleName = (prefix: string): string => {
  const moduleNames: Record<string, string> = {
    'system': '系统管理',
    'auth': '权限管理',
    'monitor': '系统监控',
    'other': '其他权限'
  }
  return moduleNames[prefix] || prefix.toUpperCase()
}

/**
 * 权限树组件
 */
export const PermissionTree: React.FC<PermissionTreeProps> = ({
  permissions,
  selectedIds,
  onSelectionChange,
  className
}) => {
  const [expandedIds, setExpandedIds] = useState<Set<number>>(new Set())
  const [searchKeyword, setSearchKeyword] = useState('')

  // 构建树形结构
  const treeData = useMemo(() => {

    if (!permissions || permissions.length === 0) {
      return []
    }

    const buildTree = (items: Permission[], parentId: number | null = null, level = 0): TreeNode[] => {
      return items
          .filter(item => {
            // 处理parentId为null、undefined或0的情况
            const itemParentId = item.parentId || null
            return itemParentId === parentId
          })
          .map(item => ({
            ...item,
            level,
            children: buildTree(items, item.id, level + 1)
          }))
          .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
    }

    // 如果所有权限都是顶级权限（parentId为null），直接按模块分组
    const hasHierarchy = permissions.some(p => p.parentId !== null && p.parentId !== undefined)

    if (!hasHierarchy) {
      // 按权限编码的前缀分组（如 system:user, auth:role 等）
      const groupedPermissions = permissions.reduce((groups, permission) => {
        const prefix = permission.permissionCode.split(':')[0] || 'other'
        if (!groups[prefix]) {
          groups[prefix] = []
        }
        groups[prefix].push({
          ...permission,
          level: 1,
          children: []
        })
        return groups
      }, {} as Record<string, TreeNode[]>)

      // 转换为树形结构，每个模块作为一个顶级节点
      return Object.entries(groupedPermissions).map(([prefix, items], index) => ({
        id: -index - 1, // 使用负数ID避免与真实权限ID冲突
        permissionName: getModuleName(prefix),
        permissionCode: prefix,
        permissionType: 'MODULE',
        status: 1,
        sortOrder: index,
        level: 0,
        children: items.map(item => ({...item, level: 1})),
        createTime: '',
        updateTime: '',
        deleted: 0,
        tenantId: 1, // 添加必需的tenantId字段
        createBy: undefined,
        updateBy: undefined,
        remark: undefined,
        parentId: undefined,
        resourcePath: undefined,
        method: undefined
      } as TreeNode))
    }

    return buildTree(permissions)
  }, [permissions])

  // 过滤树节点（搜索功能）
  const filteredTreeData = useMemo(() => {
    if (!searchKeyword.trim()) return treeData

    const filterTree = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.reduce<TreeNode[]>((acc, node) => {
        const matchesSearch =
          node.permissionName.toLowerCase().includes(searchKeyword.toLowerCase()) ||
          (node.permissionCode && node.permissionCode.toLowerCase().includes(searchKeyword.toLowerCase()))

        const filteredChildren = filterTree(node.children)
        
        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...node,
            children: filteredChildren
          })
        }

        return acc
      }, [])
    }

    return filterTree(treeData)
  }, [treeData, searchKeyword])

  // 获取所有子节点ID
  const getAllChildIds = (node: TreeNode): number[] => {
    const ids = [node.id]
    node.children.forEach(child => {
      ids.push(...getAllChildIds(child))
    })
    return ids
  }

  // 获取所有父节点ID
  const getAllParentIds = (nodeId: number): number[] => {
    const parentIds: number[] = []
    const findParents = (id: number) => {
      const node = permissions.find(p => p.id === id)
      if (node && node.parentId) {
        parentIds.push(node.parentId)
        findParents(node.parentId)
      }
    }
    findParents(nodeId)
    return parentIds
  }

  // 检查节点选中状态
  const getNodeCheckState = (node: TreeNode): 'checked' | 'unchecked' | 'indeterminate' => {
    const allChildIds = getAllChildIds(node)
    const selectedChildIds = allChildIds.filter(id => selectedIds.includes(id))
    
    if (selectedChildIds.length === 0) return 'unchecked'
    if (selectedChildIds.length === allChildIds.length) return 'checked'
    return 'indeterminate'
  }

  // 处理节点选中状态变化
  const handleNodeCheck = (node: TreeNode, checked: boolean) => {
    const allChildIds = getAllChildIds(node)
    let newSelectedIds = [...selectedIds]

    if (checked) {
      // 选中：添加当前节点及所有子节点
      allChildIds.forEach(id => {
        if (!newSelectedIds.includes(id)) {
          newSelectedIds.push(id)
        }
      })

      // 检查是否需要选中父节点
      const parentIds = getAllParentIds(node.id)
      parentIds.forEach(parentId => {
        const parentNode = permissions.find(p => p.id === parentId)
        if (parentNode) {
          const parentAllChildIds = getAllChildIds({ ...parentNode, children: [], level: 0 } as TreeNode)
          const parentSelectedChildIds = parentAllChildIds.filter(id => newSelectedIds.includes(id))

          // 如果父节点的所有子节点都被选中，则选中父节点
          if (parentSelectedChildIds.length === parentAllChildIds.length && !newSelectedIds.includes(parentId)) {
            newSelectedIds.push(parentId)
          }
        }
      })
    } else {
      // 取消选中：移除当前节点及所有子节点
      newSelectedIds = newSelectedIds.filter(id => !allChildIds.includes(id))

      // 更新父节点状态：只有当父节点的所有子节点都未选中时，才取消选中父节点
      const parentIds = getAllParentIds(node.id)
      parentIds.forEach(parentId => {
        const parentNode = permissions.find(p => p.id === parentId)
        if (parentNode) {
          const parentAllChildIds = getAllChildIds({ ...parentNode, children: [], level: 0 } as TreeNode)
          const parentSelectedChildIds = parentAllChildIds.filter(id => newSelectedIds.includes(id))

          // 只有当父节点的所有子节点都未选中时，才取消选中父节点
          if (parentSelectedChildIds.length === 0 && newSelectedIds.includes(parentId)) {
            newSelectedIds = newSelectedIds.filter(id => id !== parentId)
          }
        }
      })
    }

    onSelectionChange(newSelectedIds)
  }

  // 切换节点展开状态
  const toggleExpanded = (nodeId: number) => {
    const newExpandedIds = new Set(expandedIds)
    if (newExpandedIds.has(nodeId)) {
      newExpandedIds.delete(nodeId)
    } else {
      newExpandedIds.add(nodeId)
    }
    setExpandedIds(newExpandedIds)
  }

  // 全选/反选
  const handleSelectAll = () => {
    const allIds = permissions.map(p => p.id)
    const isAllSelected = allIds.every(id => selectedIds.includes(id))
    
    if (isAllSelected) {
      onSelectionChange([])
    } else {
      onSelectionChange(allIds)
    }
  }

  // 展开/收起所有节点
  const handleExpandAll = () => {
    const allIds = new Set<number>()

    // 递归收集所有节点ID（包括虚拟的模块节点）
    const collectAllIds = (nodes: TreeNode[]) => {
      nodes.forEach(node => {
        allIds.add(node.id)
        if (node.children && node.children.length > 0) {
          collectAllIds(node.children)
        }
      })
    }

    collectAllIds(filteredTreeData)
    console.log('📋 要展开的节点IDs:', Array.from(allIds))
    setExpandedIds(allIds)
  }

  const handleCollapseAll = () => {
    console.log('🔄 收起所有节点')
    setExpandedIds(new Set())
  }

  // 渲染树节点
  const renderTreeNode = (node: TreeNode) => {
    const isExpanded = expandedIds.has(node.id)
    const hasChildren = node.children.length > 0
    const checkState = getNodeCheckState(node)
    const isChecked = checkState === 'checked'
    const isIndeterminate = checkState === 'indeterminate'

    return (
      <div key={node.id} className="select-none">
        <div
          className={cn(
            'flex items-center py-1 px-2 hover:bg-muted/50 rounded-sm',
            'transition-colors duration-150'
          )}
          style={{ paddingLeft: `${node.level * 20 + 8}px` }}
        >
          {/* 展开/收起按钮 */}
          <div className="w-4 h-4 mr-1 flex items-center justify-center">
            {hasChildren ? (
              <button
                onClick={() => toggleExpanded(node.id)}
                className="p-0.5 hover:bg-muted rounded"
              >
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </button>
            ) : null}
          </div>

          {/* 复选框 */}
          <Checkbox
            checked={isChecked}
            indeterminate={isIndeterminate}
            onCheckedChange={(checked) => handleNodeCheck(node, Boolean(checked))}
            className="mr-2"
          />

          {/* 权限名称 */}
          <span className="flex-1 text-sm">
            {node.permissionName}
          </span>

          {/* 权限编码 */}
          <span className="text-xs text-muted-foreground ml-2">
            {node.permissionCode}
          </span>
        </div>

        {/* 子节点 */}
        {hasChildren && isExpanded && (
          <div>
            {node.children.map(child => renderTreeNode(child))}
          </div>
        )}
      </div>
    )
  }

  const allIds = permissions.map(p => p.id)
  const selectedCount = selectedIds.length
  const totalCount = allIds.length
  const isAllSelected = selectedCount === totalCount
  const isIndeterminate = selectedCount > 0 && selectedCount < totalCount

  return (
    <div className={cn('space-y-4', className)}>
      {/* 工具栏 */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <Checkbox
            checked={isAllSelected}
            ref={(el) => {
              if (el) {
                el.indeterminate = isIndeterminate
              }
            }}
            onCheckedChange={handleSelectAll}
          />
          <span className="text-sm font-medium">
            全选 ({selectedCount}/{totalCount})
          </span>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExpandAll}
          >
            展开全部
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleCollapseAll}
          >
            收起全部
          </Button>
        </div>
      </div>

      {/* 搜索框 */}
      <div className="relative">
        <Input
          placeholder="搜索权限..."
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          className="pr-8"
        />
      </div>

      {/* 权限树 */}
      <div className="border rounded-md max-h-96 overflow-y-auto">
        <div className="p-2">
          {permissions.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <div className="text-lg mb-2">🔍</div>
              <div>暂无权限数据</div>
              <div className="text-xs mt-1">请检查权限配置或联系管理员</div>
            </div>
          ) : filteredTreeData.length > 0 ? (
            filteredTreeData.map(node => renderTreeNode(node))
          ) : (
            <div className="text-center text-muted-foreground py-8">
              <div className="text-lg mb-2">🔍</div>
              <div>未找到匹配的权限</div>
              <div className="text-xs mt-1">尝试修改搜索关键词</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default PermissionTree
