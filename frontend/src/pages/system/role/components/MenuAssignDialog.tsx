/**
 * 菜单分配对话框组件
 * 
 * 用于为角色分配菜单的对话框
 */

import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Alert,
  AlertDescription,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui'
import { Loader2, AlertCircle, Menu, Shield, Info } from 'lucide-react'
import { useToast } from '@/hooks'
import { RoleService } from '@/services/role'
import { MenuService } from '@/services/menu'
import type { Role } from '@/types/role'
import type { Menu as MenuType } from '@/types/api'
import { MenuTree } from './MenuTree'
// 注意：权限管理已整合到菜单管理中，不再需要独立的 PermissionTree

/**
 * 后端菜单数据结构（与前端不同）
 */
interface BackendMenu {
  id: number
  parentId?: number
  menuName: string      // 后端字段名
  menuType: number      // 后端字段名
  path?: string
  component?: string
  icon?: string
  permissionCode?: string
  status: number        // 后端字段名
  visible: number       // 后端字段名
  sortOrder: number     // 后端字段名
  remark?: string
  children?: BackendMenu[]
}

/**
 * 转换后端菜单数据为前端格式
 */
const convertBackendMenuToFrontend = (backendMenu: BackendMenu): MenuType => {
  return {
    id: backendMenu.id,
    parentId: backendMenu.parentId,
    name: backendMenu.menuName,           // 字段名转换
    title: backendMenu.menuName,          // 使用 menuName 作为 title
    path: backendMenu.path,
    component: backendMenu.component,
    icon: backendMenu.icon,
    permission: backendMenu.permissionCode, // 字段名转换
    type: backendMenu.menuType,           // 字段名转换
    sort: backendMenu.sortOrder,          // 字段名转换
    visible: backendMenu.visible === 1,   // 数字转布尔值
    enabled: backendMenu.status === 1,    // 数字转布尔值
    children: backendMenu.children ? backendMenu.children.map(convertBackendMenuToFrontend) : []
  }
}

/**
 * 转换后端菜单列表为前端格式
 */
const convertBackendMenusToFrontend = (backendMenus: BackendMenu[]): MenuType[] => {
  return backendMenus.map(convertBackendMenuToFrontend)
}

interface MenuAssignDialogProps {
  /** 是否打开 */
  open: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 角色信息 */
  role?: Role
  /** 分配成功回调 */
  onSuccess?: () => void
}

/**
 * 菜单分配对话框组件
 */
const MenuAssignDialog: React.FC<MenuAssignDialogProps> = ({
  open,
  onClose,
  role,
  onSuccess
}) => {
  const { toast } = useToast()

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [menus, setMenus] = useState<MenuType[]>([])
  const [selectedMenuIds, setSelectedMenuIds] = useState<number[]>([])
  // 统一权限管理：权限通过菜单中的按钮类型统一管理，不再需要独立的权限状态

  /**
   * 加载数据
   */
  const loadData = async () => {
    if (!role) return

    try {
      setLoading(true)

      // 加载菜单列表和角色已分配的菜单（统一权限管理，不再需要独立的权限API）
      const [menusResponse, roleMenusResponse] = await Promise.all([
        MenuService.getMenuTree(),
        RoleService.getRoleMenus(role.id)
      ])

      // 转换后端菜单数据为前端格式
      const convertedMenus = convertBackendMenusToFrontend(menusResponse as BackendMenu[] || [])
      setMenus(convertedMenus)
      setSelectedMenuIds(roleMenusResponse.map((menu: any) => menu.id) || [])
    } catch (error) {
      console.error('加载数据失败:', error)
      toast({
        title: '加载失败',
        description: '加载菜单和权限数据失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  /**
   * 保存菜单权限分配
   */
  const handleSave = async () => {
    if (!role) return

    try {
      setSaving(true)
      
      // 统一保存菜单分配（包含权限）
      await RoleService.assignMenus(role.id, selectedMenuIds)
      
      toast({
        title: '分配成功',
        description: '权限分配成功'
      })

      onSuccess?.()
      onClose()
    } catch (error) {
      console.error('权限分配失败:', error)
      toast({
        title: '分配失败',
        description: '权限分配失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  /**
   * 处理关闭
   */
  const handleClose = () => {
    if (saving) return
    onClose()
  }

  // 监听对话框打开状态
  useEffect(() => {
    if (open && role) {
      loadData()
    }
  }, [open, role])

  // 重置状态
  useEffect(() => {
    if (!open) {
      setSelectedMenuIds([])
      setMenus([])
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            角色权限管理
          </DialogTitle>
          <DialogDescription>
            为角色 <span className="font-semibold text-foreground">"{role?.roleName}"</span> 分配菜单访问权限和操作权限。统一管理页面可见性和功能权限点。
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span>加载中...</span>
          </div>
        ) : (
          <div className="space-y-4">
            {/* 说明信息 */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <div><strong>统一权限管理：</strong>在此界面统一管理角色的所有权限，包括菜单访问权限和操作权限</div>
                  <div><strong>菜单权限：</strong>目录和菜单类型控制页面可见性，按钮类型控制具体操作权限</div>
                  <div className="text-muted-foreground text-sm">提示：选择菜单时会自动包含其下的权限点，无需单独配置权限</div>
                </div>
              </AlertDescription>
            </Alert>

            {/* 统一权限分配 */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Menu className="w-4 h-4 mr-2" />
                  <span className="font-medium">权限分配</span>
                  <span className="ml-2 text-sm text-muted-foreground">
                    (已选择 {selectedMenuIds.length} 项)
                  </span>
                </div>
              </div>

              <div className="text-sm text-muted-foreground">
                选择角色可以访问的菜单和操作权限。目录和菜单类型控制页面可见性，按钮类型控制具体操作权限。
              </div>

              <MenuTree
                menus={menus}
                selectedIds={selectedMenuIds}
                onSelectionChange={setSelectedMenuIds}
                className="border-0"
                showPermissionInfo={true}
              />
            </div>


          </div>
        )}

        <DialogFooter className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            已选择 {selectedMenuIds.length} 个菜单项
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={saving}
            >
              取消
            </Button>
            
            <Button
              onClick={handleSave}
              disabled={saving || loading}
              className="min-w-[100px]"
            >
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  保存中...
                </>
              ) : (
                '保存分配'
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default MenuAssignDialog
