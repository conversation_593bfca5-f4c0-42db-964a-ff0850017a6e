import React, { useEffect, useState } from 'react'
import { Save, RotateCcw } from 'lucide-react'
import { useRequest } from 'ahooks'
import { Button, Input, Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Textarea } from '@/components/ui'
import { useMenuStore } from '../hooks'
import { MenuAction } from '../types'
import type {
  MenuItem,
  MenuCreateRequest,
  MenuUpdateRequest,
  MenuFormData,
  MenuTreeNode
} from '../types'

/**
 * 菜单表单组件属性
 */
interface MenuFormProps {
  /** 是否显示 */
  visible: boolean
  /** 操作类型 */
  action: MenuAction
  /** 当前菜单数据 */
  currentMenu?: MenuItem
  /** 父菜单ID */
  parentId?: number
  /** 关闭回调 */
  onClose: () => void
  /** 提交成功回调 */
  onSuccess?: (action: MenuAction, menuName?: string) => void
  /** 提交失败回调 */
  onError?: (action: MenuAction, error: string) => void
}

/**
 * 菜单表单组件
 */
export const MenuForm: React.FC<MenuFormProps> = ({
  visible,
  action,
  currentMenu,
  parentId,
  onClose,
  onSuccess,
  onError
}) => {
  const { createMenu, updateMenu, validateMenuName, validateMenuPath } = useMenuStore()

  // 表单数据
  const [formData, setFormData] = useState<Partial<MenuFormData>>({
    parentId: parentId || 0,
    menuName: '',
    menuType: 1, // 默认为页面
    path: '',
    component: '',
    icon: '',
    permissionCode: '',
    status: 1, // 默认启用
    visible: 1, // 默认显示
    sortOrder: 0,
    remark: ''
  })

  // 验证错误
  const [errors, setErrors] = useState<Record<string, string>>({})

  /**
   * 初始化表单数据
   */
  useEffect(() => {
    if (visible) {
      if (action === MenuAction.EDIT && currentMenu) {
        // 编辑模式，填充现有数据
        setFormData({
          id: currentMenu.id,
          parentId: currentMenu.parentId,
          menuName: currentMenu.name || currentMenu.title, // 使用 name 字段，fallback 到 title
          menuType: currentMenu.type, // 后端字段映射
          path: currentMenu.path || '',
          component: currentMenu.component || '',
          icon: currentMenu.icon || '',
          permissionCode: currentMenu.permission || '', // 后端字段映射
          status: currentMenu.enabled ? 1 : 0, // 后端字段映射
          visible: currentMenu.visible ? 1 : 0, // 后端字段映射
          sortOrder: currentMenu.sort, // 后端字段映射
          remark: ''
        })
      } else {
        // 新增模式，重置表单
        setFormData({
          parentId: parentId || 0,
          menuName: '',
          menuType: 1,
          path: '',
          component: '',
          icon: '',
          permissionCode: '',
          status: 1,
          visible: 1,
          sortOrder: 0,
          remark: ''
        })
      }
      setErrors({})
    }
  }, [visible, action, currentMenu, parentId])

  /**
   * 表单字段变化处理
   */
  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  /**
   * 表单验证
   */
  const validateForm = async (): Promise<boolean> => {
    const newErrors: Record<string, string> = {}

    // 必填字段验证
    if (!formData.menuName?.trim()) {
      newErrors.menuName = '菜单名称不能为空'
    }

    if (formData.menuType === 1 && !formData.path?.trim()) {
      newErrors.path = '页面类型菜单必须设置路由路径'
    }

    if (formData.menuType === 1 && !formData.component?.trim()) {
      newErrors.component = '页面类型菜单必须设置组件路径'
    }

    if (formData.menuType === 2 && !formData.permissionCode?.trim()) {
      newErrors.permissionCode = '按钮类型菜单必须设置权限标识'
    }

    // 异步验证
    if (formData.menuName?.trim()) {
      const isNameValid = await validateMenuName(
        formData.menuName.trim(),
        formData.parentId || 0,
        action === MenuAction.EDIT ? (formData as MenuUpdateRequest).id : undefined
      )
      if (!isNameValid) {
        newErrors.menuName = '菜单名称已存在'
      }
    }

    if (formData.path?.trim()) {
      const isPathValid = await validateMenuPath(
        formData.path.trim(),
        action === MenuAction.EDIT ? (formData as MenuUpdateRequest).id : undefined
      )
      if (!isPathValid) {
        newErrors.path = '路由路径已存在'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  /**
   * 提交表单
   */
  const { run: handleSubmit, loading: submitting } = useRequest(
    async () => {
      const isValid = await validateForm()
      if (!isValid) {
        throw new Error('表单验证失败')
      }

      let success = false
      if (action === MenuAction.EDIT) {
        success = await updateMenu(formData as MenuUpdateRequest)
      } else {
        success = await createMenu(formData as MenuCreateRequest)
      }

      if (!success) {
        throw new Error('操作失败')
      }

      return success
    },
    {
      manual: true,
      onSuccess: () => {
        onSuccess?.()
        onClose()
      },
      onError: (error) => {
      }
    }
  )

  /**
   * 重置表单
   */
  const handleReset = () => {
    if (action === MenuAction.EDIT && currentMenu) {
      setFormData({
        id: currentMenu.id,
        parentId: currentMenu.parentId,
        menuName: currentMenu.name || currentMenu.title, // 使用 name 字段，fallback 到 title
        menuType: currentMenu.type, // 后端字段映射
        path: currentMenu.path || '',
        component: currentMenu.component || '',
        icon: currentMenu.icon || '',
        permissionCode: currentMenu.permission || '', // 后端字段映射
        status: currentMenu.enabled ? 1 : 0, // 后端字段映射
        visible: currentMenu.visible ? 1 : 0, // 后端字段映射
        sortOrder: currentMenu.sort, // 后端字段映射
        remark: ''
      })
    } else {
      setFormData({
        parentId: parentId || 0,
        menuName: '',
        menuType: 1,
        path: '',
        component: '',
        icon: '',
        permissionCode: '',
        status: 1,
        visible: 1,
        sortOrder: 0,
        remark: ''
      })
    }
    setErrors({})
  }

  if (!visible) {
    return null
  }

  return (
    <Dialog open={visible} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[625px]">
        <DialogHeader>
          <DialogTitle>{action === MenuAction.EDIT ? '编辑菜单' : '新增菜单'}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
            {/* 基本信息 */}

              {/* 菜单名称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  菜单名称 <span className="text-red-500">*</span>
                </label>
                <Input
                  value={formData.menuName || ''}
                  onChange={(e) => handleFieldChange('menuName', e.target.value)}
                  placeholder="请输入菜单名称"
                  className={errors.menuName ? 'border-red-500' : ''}
                />
                {errors.menuName && (
                  <p className="mt-1 text-sm text-red-500">{errors.menuName}</p>
                )}
              </div>

              {/* 菜单类型 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  菜单类型 <span className="text-red-500">*</span>
                </label>
                <Select value={formData.menuType?.toString() || '1'} onValueChange={(value) => handleFieldChange('menuType', Number(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择菜单类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">目录</SelectItem>
                    <SelectItem value="1">菜单</SelectItem>
                    <SelectItem value="2">按钮</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 路由配置 */}
            {formData.menuType === 1 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 路由路径 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    路由路径 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData.path || ''}
                    onChange={(e) => handleFieldChange('path', e.target.value)}
                    placeholder="如: /system/user"
                    className={errors.path ? 'border-red-500' : ''}
                  />
                  {errors.path && (
                    <p className="mt-1 text-sm text-red-500">{errors.path}</p>
                  )}
                </div>

                {/* 组件路径 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    组件路径 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData.component || ''}
                    onChange={(e) => handleFieldChange('component', e.target.value)}
                    placeholder="如: system/user/UserList"
                    className={errors.component ? 'border-red-500' : ''}
                  />
                  {errors.component && (
                    <p className="mt-1 text-sm text-red-500">{errors.component}</p>
                  )}
                </div>
              </div>
            )}

            {/* 权限配置 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 权限标识 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  权限标识 {formData.menuType === 2 && <span className="text-red-500">*</span>}
                </label>
                <Input
                  value={formData.permissionCode || ''}
                  onChange={(e) => handleFieldChange('permissionCode', e.target.value)}
                  placeholder="如: system:user:list"
                  className={errors.permissionCode ? 'border-red-500' : ''}
                />
                {errors.permissionCode && (
                  <p className="mt-1 text-sm text-red-500">{errors.permissionCode}</p>
                )}
              </div>

              {/* 菜单图标 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  菜单图标
                <Input
                  value={formData.icon || ''}
                  onChange={(e) => handleFieldChange('icon', e.target.value)}
                  placeholder="如: Users"
                />
                </label>
              </div>
            </div>

            {/* 显示配置 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 状态 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  状态
                </label>
                <Select value={formData.status?.toString() || '1'} onValueChange={(value) => handleFieldChange('status', Number(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">启用</SelectItem>
                    <SelectItem value="0">禁用</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 可见性 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  可见性
                </label>
                <Select value={formData.visible?.toString() || '1'} onValueChange={(value) => handleFieldChange('visible', Number(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择可见性" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">显示</SelectItem>
                    <SelectItem value="0">隐藏</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 排序 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  排序
                </label>
                <Input
                  type="number"
                  value={formData.sortOrder || 0}
                  onChange={(e) => handleFieldChange('sortOrder', Number(e.target.value))}
                  placeholder="0"
                  min="0"
                />
              </div>
            </div>

            {/* 备注 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                备注
              </label>
              <Textarea
                value={formData.remark || ''}
                onChange={(e) => handleFieldChange('remark', e.target.value)}
                placeholder="请输入备注信息"
                rows={3}
              />
            </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleReset} disabled={submitting}>
            <RotateCcw className="w-4 h-4 mr-2" />
            重置
          </Button>
          <Button variant="outline" onClick={onClose} disabled={submitting}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={submitting}>
            <Save className="w-4 h-4 mr-2" />
            {submitting ? '保存中...' : '保存'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
