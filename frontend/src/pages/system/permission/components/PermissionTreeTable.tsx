/**
 * 权限树形表格组件
 * 
 * 统一重构版本 - 完整的权限树形展示和操作功能
 */

import React, { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Badge,
  Button,
  Checkbox
} from '@/components/ui'
import {
  ChevronRight,
  ChevronDown,
  Edit,
  Trash2,
  Plus,
  Shield,
  UserCheck,
  UserX
} from 'lucide-react'
import { ActionPermissionButton } from '@/components/auth/PermissionWrapper'
import type { Permission, PermissionTreeNode } from '@/types/permission'
import {
  getPermissionStatusLabel,
  getPermissionStatusColor,
  getPermissionTypeLabel,
  getPermissionTypeColor,
  PermissionStatus
} from '@/types/permission'

/**
 * 权限树形表格属性
 */
export interface PermissionTreeTableProps {
  /** 权限数据 */
  permissions: Permission[]
  /** 加载状态 */
  loading?: boolean
  /** 选中的权限ID列表 */
  selectedIds?: number[]
  /** 选中状态变化回调 */
  onSelectionChange?: (selectedIds: number[]) => void
  /** 编辑回调 */
  onEdit?: (permission: Permission) => void
  /** 删除回调 */
  onDelete?: (permission: Permission) => void
  /** 新增子权限回调 */
  onAddChild?: (parentPermission: Permission) => void
  /** 状态切换回调 */
  onToggleStatus?: (permission: Permission) => void
}

/**
 * 权限树形表格组件
 */
const PermissionTreeTable: React.FC<PermissionTreeTableProps> = ({
  permissions,
  loading = false,
  selectedIds = [],
  onSelectionChange,
  onEdit,
  onDelete,
  onAddChild,
  onToggleStatus
}) => {
  const [expandedIds, setExpandedIds] = useState<Set<number>>(new Set())

  /**
   * 切换展开状态
   */
  const toggleExpanded = (id: number) => {
    const newExpandedIds = new Set(expandedIds)
    if (newExpandedIds.has(id)) {
      newExpandedIds.delete(id)
    } else {
      newExpandedIds.add(id)
    }
    setExpandedIds(newExpandedIds)
  }

  /**
   * 处理全选/取消全选
   */
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = getAllPermissionIds(permissions)
      onSelectionChange?.(allIds)
    } else {
      onSelectionChange?.([])
    }
  }

  /**
   * 处理单个选择
   */
  const handleSelectOne = (id: number, checked: boolean) => {
    const newSelectedIds = checked
      ? [...selectedIds, id]
      : selectedIds.filter(selectedId => selectedId !== id)
    onSelectionChange?.(newSelectedIds)
  }

  /**
   * 获取所有权限ID
   */
  const getAllPermissionIds = (perms: Permission[]): number[] => {
    const ids: number[] = []
    const traverse = (items: Permission[]) => {
      items.forEach(item => {
        ids.push(item.id)
        if (item.children) {
          traverse(item.children)
        }
      })
    }
    traverse(perms)
    return ids
  }

  /**
   * 渲染权限行
   */
  const renderPermissionRow = (permission: Permission, level: number = 0): React.ReactNode[] => {
    const hasChildren = permission.children && permission.children.length > 0
    const isExpanded = expandedIds.has(permission.id)
    const isSelected = selectedIds.includes(permission.id)

    const rows: React.ReactNode[] = [
      <TableRow key={permission.id} className="hover:bg-muted/50">
        {/* 选择框 */}
        <TableCell className="w-12">
          <Checkbox
            checked={isSelected}
            onCheckedChange={(checked) => handleSelectOne(permission.id, checked as boolean)}
          />
        </TableCell>

        {/* 权限名称（带层级缩进和展开图标） */}
        <TableCell>
          <div className="flex items-center" style={{ paddingLeft: `${level * 24}px` }}>
            {hasChildren ? (
              <Button
                variant="ghost"
                size="sm"
                className="w-6 h-6 p-0 mr-2"
                onClick={() => toggleExpanded(permission.id)}
              >
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </Button>
            ) : (
              <div className="w-6 h-6 mr-2" />
            )}
            <Shield className="w-4 h-4 mr-2 text-muted-foreground" />
            <div>
              <div className="font-medium">{permission.permissionName}</div>
              <div className="text-sm text-muted-foreground">{permission.permissionCode}</div>
            </div>
          </div>
        </TableCell>

        {/* 权限类型 */}
        <TableCell>
          <Badge className={getPermissionTypeColor(permission.permissionType)}>
            {getPermissionTypeLabel(permission.permissionType)}
          </Badge>
        </TableCell>

        {/* 资源路径 */}
        <TableCell>
          <div className="max-w-xs truncate" title={permission.resourcePath}>
            {permission.resourcePath || '-'}
          </div>
        </TableCell>

        {/* HTTP方法 */}
        <TableCell>
          {permission.method ? (
            <Badge variant="outline" className="text-xs">
              {permission.method}
            </Badge>
          ) : (
            '-'
          )}
        </TableCell>

        {/* 排序 */}
        <TableCell>{permission.sortOrder}</TableCell>

        {/* 状态 */}
        <TableCell>
          <Badge className={getPermissionStatusColor(permission.status)}>
            {getPermissionStatusLabel(permission.status)}
          </Badge>
        </TableCell>

        {/* 操作 */}
        <TableCell>
          <div className="flex items-center gap-1">
            {/* 编辑按钮 - 蓝色 */}
            <ActionPermissionButton
              module="permission"
              action="edit"
              config={{
                text: '',
                icon: Edit,
                variant: 'ghost',
                size: 'sm',
                onClick: () => onEdit?.(permission),
                title: '编辑权限',
                className: 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
              }}
            />
            
            {/* 新增子权限按钮 - 绿色 */}
            <ActionPermissionButton
              module="permission"
              action="add"
              config={{
                text: '',
                icon: Plus,
                variant: 'ghost',
                size: 'sm',
                onClick: () => onAddChild?.(permission),
                title: '新增子权限',
                className: 'text-green-600 hover:text-green-700 hover:bg-green-50'
              }}
            />
            
            {/* 状态切换按钮 - 动态颜色 */}
            <ActionPermissionButton
              module="permission"
              action="edit"
              config={{
                text: '',
                icon: permission.status === PermissionStatus.ENABLED ? UserX : UserCheck,
                variant: 'ghost',
                size: 'sm',
                onClick: () => onToggleStatus?.(permission),
                title: permission.status === PermissionStatus.ENABLED ? '禁用权限' : '启用权限',
                className: permission.status === PermissionStatus.ENABLED 
                  ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                  : 'text-green-600 hover:text-green-700 hover:bg-green-50'
              }}
            />
            
            {/* 删除按钮 - 红色 */}
            <ActionPermissionButton
              module="permission"
              action="delete"
              config={{
                text: '',
                icon: Trash2,
                variant: 'ghost',
                size: 'sm',
                onClick: () => onDelete?.(permission),
                title: '删除权限',
                className: 'text-red-600 hover:text-red-700 hover:bg-red-50'
              }}
            />
          </div>
        </TableCell>
      </TableRow>
    ]

    // 如果展开且有子权限，递归渲染子权限
    if (isExpanded && hasChildren) {
      permission.children!.forEach(child => {
        rows.push(...renderPermissionRow(child, level + 1))
      })
    }

    return rows
  }

  const allPermissionIds = getAllPermissionIds(permissions)
  const isAllSelected = allPermissionIds.length > 0 && selectedIds.length === allPermissionIds.length
  const isIndeterminate = selectedIds.length > 0 && selectedIds.length < allPermissionIds.length

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  if (permissions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <div className="text-center space-y-2">
          <Shield className="w-12 h-12 mx-auto opacity-50" />
          <p>暂无权限数据</p>
        </div>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                indeterminate={isIndeterminate}
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            <TableHead>权限名称</TableHead>
            <TableHead>类型</TableHead>
            <TableHead>资源路径</TableHead>
            <TableHead>方法</TableHead>
            <TableHead>排序</TableHead>
            <TableHead>状态</TableHead>
            <TableHead className="w-32">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {permissions.map(permission => renderPermissionRow(permission))}
        </TableBody>
      </Table>
    </div>
  )
}

export default PermissionTreeTable
