/**
 * 删除确认对话框组件
 * 
 * 统一重构版本 - 权限删除确认功能
 */

import React from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Alert,
  AlertDescription
} from '@/components/ui'
import { Loader2, AlertTriangle, Shield } from 'lucide-react'
import type { Permission } from '@/types/permission'

/**
 * 删除确认对话框属性
 */
export interface DeleteConfirmDialogProps {
  /** 是否显示 */
  open: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 确认删除回调 */
  onConfirm: () => Promise<void>
  /** 要删除的权限 */
  permission?: Permission
  /** 要删除的权限列表（批量删除） */
  permissions?: Permission[]
  /** 加载状态 */
  loading?: boolean
}

/**
 * 删除确认对话框组件
 */
const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  open,
  onClose,
  onConfirm,
  permission,
  permissions,
  loading = false
}) => {
  const isBatch = !permission && permissions && permissions.length > 0
  const isSingle = !!permission

  /**
   * 处理确认删除
   */
  const handleConfirm = async () => {
    try {
      await onConfirm()
    } catch (error) {
      console.error('删除权限失败:', error)
    }
  }

  /**
   * 检查是否有子权限
   */
  const hasChildren = (perm: Permission): undefined | boolean => {
    return perm.children && perm.children.length > 0
  }

  /**
   * 获取子权限数量
   */
  const getChildrenCount = (perm: Permission): number => {
    if (!perm.children) return 0
    let count = perm.children.length
    perm.children.forEach(child => {
      count += getChildrenCount(child)
    })
    return count
  }

  /**
   * 渲染单个删除内容
   */
  const renderSingleDelete = () => {
    if (!permission) return null

    const childrenCount = getChildrenCount(permission)
    const hasChildrenWarning = childrenCount > 0

    return (
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          <Shield className="w-5 h-5 text-muted-foreground mt-0.5" />
          <div className="flex-1 min-w-0">
            <div className="font-medium">{permission.permissionName}</div>
            <div className="text-sm text-muted-foreground">{permission.permissionCode}</div>
            {permission.remark && (
              <div className="text-sm text-muted-foreground mt-1">{permission.remark}</div>
            )}
          </div>
        </div>

        {hasChildrenWarning && (
          <Alert className="border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              该权限包含 <strong>{childrenCount}</strong> 个子权限，删除后所有子权限也将被删除，此操作不可恢复。
            </AlertDescription>
          </Alert>
        )}

        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            删除权限后，拥有该权限的角色将失去对应的访问权限，请确认是否继续？
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  /**
   * 渲染批量删除内容
   */
  const renderBatchDelete = () => {
    if (!permissions || permissions.length === 0) return null

    const totalChildren = permissions.reduce((total, perm) => total + getChildrenCount(perm), 0)
    const hasChildrenWarning = totalChildren > 0

    return (
      <div className="space-y-4">
        <div className="text-sm text-muted-foreground">
          即将删除 <strong>{permissions.length}</strong> 个权限：
        </div>

        <div className="max-h-32 overflow-y-auto space-y-2 border rounded-md p-3 bg-muted/30">
          {permissions.map(perm => (
            <div key={perm.id} className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-muted-foreground" />
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">{perm.permissionName}</div>
                <div className="text-xs text-muted-foreground truncate">{perm.permissionCode}</div>
              </div>
            </div>
          ))}
        </div>

        {hasChildrenWarning && (
          <Alert className="border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              这些权限包含 <strong>{totalChildren}</strong> 个子权限，删除后所有子权限也将被删除。
            </AlertDescription>
          </Alert>
        )}

        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            批量删除权限后，拥有这些权限的角色将失去对应的访问权限，此操作不可恢复，请确认是否继续？
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span>
              {isBatch ? '批量删除权限' : '删除权限'}
            </span>
          </DialogTitle>
          <DialogDescription>
            {isBatch 
              ? '您确定要删除选中的权限吗？'
              : '您确定要删除该权限吗？'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {isSingle && renderSingleDelete()}
          {isBatch && renderBatchDelete()}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            取消
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleConfirm} 
            disabled={loading}
          >
            {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            确认删除
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default DeleteConfirmDialog
