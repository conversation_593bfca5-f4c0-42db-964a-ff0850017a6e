/**
 * 权限管理页面
 *
 * 提供权限的增删改查、树形展示、状态管理等功能
 * 统一重构版本 - 完整的权限控制实现
 */

import React, { useState, useEffect, useCallback } from 'react'
import {
  Card,
  CardContent,
  Button,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Alert,
  AlertDescription
} from '@/components/ui'
import { Plus, Download, Upload, Trash2, RefreshCw, List, TreePine, Search } from 'lucide-react'
import { PagePermissionWrapper, PermissionToolbar } from '@/components/auth/PermissionWrapper'
import { useToast } from '@/hooks'
import { PermissionService } from '@/services/permission'
import {
  PermissionSearchForm,
  PermissionTable,
  PermissionTreeTable,
  PermissionForm,
  DeleteConfirmDialog
} from './components'
import type {
  Permission,
  PermissionQueryRequest,
  PermissionCreateRequest,
  PermissionUpdateRequest,
  PageResult
} from '@/types/permission'

/**
 * 权限管理主页面
 */
const PermissionList: React.FC = () => {
  const { toast } = useToast()

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [pageData, setPageData] = useState<PageResult<Permission>>({
    records: [],
    total: 0,
    pageNum: 1,
    pageSize: 20
  })
  const [selectedIds, setSelectedIds] = useState<number[]>([])
  const [searchParams, setSearchParams] = useState<PermissionQueryRequest>({
    pageNum: 1,
    pageSize: 20
  })
  const [viewMode, setViewMode] = useState<'table' | 'tree'>('tree')

  // 表单状态
  const [formOpen, setFormOpen] = useState(false)
  const [formLoading, setFormLoading] = useState(false)
  const [editingPermission, setEditingPermission] = useState<Permission | undefined>()
  const [parentId, setParentId] = useState<number | undefined>()

  // 删除确认对话框状态
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    permission: undefined as Permission | undefined,
    permissions: undefined as Permission[] | undefined,
    loading: false
  })

  /**
   * 加载权限列表
   */
  const loadPermissions = useCallback(async () => {
    try {
      setLoading(true)
      if (viewMode === 'tree') {
        const data = await PermissionService.getPermissionTree()
        console.log('🌳 树形权限数据:', data)
        setPermissions(data)
      } else {
        const data = await PermissionService.getPermissionList(searchParams)
        console.log('📋 列表权限数据:', data)
        setPageData(data)
      }
    } catch (error) {
      console.error('加载权限列表失败:', error)
      toast({
        title: '加载失败',
        description: '权限列表加载失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [searchParams, viewMode, toast])

  /**
   * 初始化数据
   */
  useEffect(() => {
    loadPermissions()
  }, [loadPermissions])

  /**
   * 处理搜索参数变化
   */
  const handleSearchParamsChange = (params: PermissionQueryRequest) => {
    setSearchParams(params)
  }

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    setSearchParams(prev => ({
      ...prev,
      pageNum: 1
    }))
  }

  /**
   * 处理重置
   */
  const handleReset = () => {
    setSearchParams({
      pageNum: 1,
      pageSize: 20
    })
  }

  /**
   * 处理刷新
   */
  const handleRefresh = () => {
    setSelectedIds([])
    loadPermissions()
  }

  /**
   * 处理分页变化
   */
  const handlePageChange = (page: number, pageSize?: number) => {
    // 确保 page 是有效的数字
    const validPage = typeof page === 'number' && !isNaN(page) && page > 0 ? page : 1
    const validPageSize = typeof pageSize === 'number' && !isNaN(pageSize) && pageSize > 0
      ? pageSize
      : (typeof searchParams.pageSize === 'number' && !isNaN(searchParams.pageSize) ? searchParams.pageSize : 20)

    setSearchParams(prev => ({
      ...prev,
      pageNum: validPage,
      pageSize: validPageSize
    }))
  }

  /**
   * 处理新增权限
   */
  const handleAdd = (parentPermission?: Permission) => {
    setEditingPermission(undefined)
    setParentId(parentPermission?.id)
    setFormOpen(true)
  }

  /**
   * 处理编辑权限
   */
  const handleEdit = (permission: Permission) => {
    setEditingPermission(permission)
    setParentId(undefined)
    setFormOpen(true)
  }

  /**
   * 处理删除权限
   */
  const handleDelete = (permission: Permission) => {
    setDeleteDialog({
      open: true,
      permission,
      permissions: undefined,
      loading: false
    })
  }

  /**
   * 处理批量删除
   */
  const handleBatchDelete = () => {
    if (selectedIds.length === 0) {
      toast({
        title: '请选择要删除的权限',
        variant: 'destructive'
      })
      return
    }

    const selectedPermissions = viewMode === 'tree'
      ? getSelectedPermissionsFromTree(permissions, selectedIds)
      : pageData.records.filter(p => selectedIds.includes(p.id))

    setDeleteDialog({
      open: true,
      permission: undefined,
      permissions: selectedPermissions,
      loading: false
    })
  }

  /**
   * 处理导入
   */
  const handleImport = () => {
    console.log('📥 导入权限')
    // TODO: 实现权限导入功能
  }

  /**
   * 处理导出
   */
  const handleExport = () => {
    console.log('📤 导出权限')
    // TODO: 实现权限导出功能
  }

  /**
   * 处理状态切换
   */
  const handleToggleStatus = async (permission: Permission) => {
    try {
      await PermissionService.updatePermissionStatus(permission.id, permission.status === 1 ? 0 : 1)
      toast({
        title: '操作成功',
        description: `权限已${permission.status === 1 ? '禁用' : '启用'}`
      })
      loadPermissions()
    } catch (error) {
      console.error('切换权限状态失败:', error)
      toast({
        title: '操作失败',
        description: '权限状态切换失败，请重试',
        variant: 'destructive'
      })
    }
  }

  /**
   * 处理表单提交
   */
  const handleFormSubmit = async (data: PermissionCreateRequest | PermissionUpdateRequest) => {
    try {
      setFormLoading(true)
      if (editingPermission) {
        await PermissionService.updatePermission(editingPermission.id, data as PermissionUpdateRequest)
        toast({
          title: '更新成功',
          description: '权限信息已更新'
        })
      } else {
        await PermissionService.createPermission(data as PermissionCreateRequest)
        toast({
          title: '创建成功',
          description: '权限已创建'
        })
      }
      setFormOpen(false)
      loadPermissions()
    } catch (error) {
      console.error('提交权限表单失败:', error)
      toast({
        title: '操作失败',
        description: editingPermission ? '权限更新失败，请重试' : '权限创建失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setFormLoading(false)
    }
  }

  /**
   * 处理确认删除
   */
  const handleConfirmDelete = async () => {
    try {
      setDeleteDialog(prev => ({ ...prev, loading: true }))

      if (deleteDialog.permission) {
        // 单个删除
        await PermissionService.deletePermission(deleteDialog.permission.id)
        toast({
          title: '删除成功',
          description: '权限已删除'
        })
      } else if (deleteDialog.permissions) {
        // 批量删除
        const ids = deleteDialog.permissions.map(p => p.id)
        await PermissionService.batchDeletePermissions(ids)
        toast({
          title: '删除成功',
          description: `已删除 ${ids.length} 个权限`
        })
      }

      setDeleteDialog({ open: false, permission: undefined, permissions: undefined, loading: false })
      setSelectedIds([])
      loadPermissions()
    } catch (error) {
      console.error('删除权限失败:', error)
      toast({
        title: '删除失败',
        description: '权限删除失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setDeleteDialog(prev => ({ ...prev, loading: false }))
    }
  }

  /**
   * 从树形数据中获取选中的权限
   */
  const getSelectedPermissionsFromTree = (perms: Permission[], ids: number[]): Permission[] => {
    const result: Permission[] = []
    const traverse = (items: Permission[]) => {
      items.forEach(item => {
        if (ids.includes(item.id)) {
          result.push(item)
        }
        if (item.children) {
          traverse(item.children)
        }
      })
    }
    traverse(perms)
    return result
  }

  return (
    <PagePermissionWrapper module="permission">
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">权限管理</h1>
          <p className="text-muted-foreground">
            管理系统中的所有权限，包括菜单权限、API权限、按钮权限等
          </p>
        </div>

        {/* 功能提示 */}
        <Alert>
          <AlertDescription>
            权限管理是系统安全的核心，请谨慎操作。删除权限可能影响用户的正常使用。
          </AlertDescription>
        </Alert>

        {/* 权限控制的工具栏 */}
        <PermissionToolbar
          module="permission"
          searchComponent={
            viewMode === 'table' ? (
              <PermissionSearchForm
                searchParams={searchParams}
                onSearchParamsChange={handleSearchParamsChange}
                onSearch={handleSearch}
                onReset={handleReset}
                loading={loading}
              />
            ) : undefined
          }
          primaryActions={[
            {
              action: 'add',
              config: {
                text: '新增权限',
                icon: Plus,
                onClick: () => handleAdd(),
                disabled: loading,
              }
            }
          ]}
          secondaryActions={[
            {
              action: 'import',
              config: {
                text: '导入',
                icon: Upload,
                variant: 'outline',
                onClick: handleImport,
                disabled: loading,
              }
            },
            {
              action: 'export',
              config: {
                text: '导出',
                icon: Download,
                variant: 'outline',
                onClick: handleExport,
                disabled: loading,
              }
            },
            {
              action: 'delete',
              config: {
                text: '批量删除',
                icon: Trash2,
                variant: 'outline',
                onClick: handleBatchDelete,
                disabled: loading || selectedIds.length === 0,
              }
            }
          ]}
          customActions={
            <div className="flex items-center space-x-2">
              <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'table' | 'tree')}>
                <TabsList>
                  <TabsTrigger value="tree">
                    <TreePine className="w-4 h-4 mr-2" />
                    树形视图
                  </TabsTrigger>
                  <TabsTrigger value="table">
                    <List className="w-4 h-4 mr-2" />
                    列表视图
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            </div>
          }
        />

        {/* 主要内容区域 */}
        <Card>
          <CardContent className="p-6">
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'table' | 'tree')}>
              <TabsContent value="tree" className="mt-0">
                <PermissionTreeTable
                  permissions={permissions}
                  loading={loading}
                  selectedIds={selectedIds}
                  onSelectionChange={setSelectedIds}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onAddChild={handleAdd}
                  onToggleStatus={handleToggleStatus}
                />
              </TabsContent>

              <TabsContent value="table" className="mt-0">
                <PermissionTable
                  data={pageData}
                  loading={loading}
                  selectedIds={selectedIds}
                  onSelectionChange={setSelectedIds}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onAddChild={handleAdd}
                  onToggleStatus={handleToggleStatus}
                  onPageChange={handlePageChange}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* 权限表单对话框 */}
        <PermissionForm
          open={formOpen}
          onClose={() => setFormOpen(false)}
          onSubmit={handleFormSubmit}
          permission={editingPermission}
          parentId={parentId}
          loading={formLoading}
        />

        {/* 删除确认对话框 */}
        <DeleteConfirmDialog
          open={deleteDialog.open}
          onClose={() => setDeleteDialog({ open: false, permission: undefined, permissions: undefined, loading: false })}
          onConfirm={handleConfirmDelete}
          permission={deleteDialog.permission}
          permissions={deleteDialog.permissions}
          loading={deleteDialog.loading}
        />
      </div>
    </PagePermissionWrapper>
  )
}

export default PermissionList
