/**
 * 租户管理性能优化Hook
 * 
 * 提供性能优化相关的功能，如虚拟化、防抖、缓存等
 */

import { useMemo, useCallback, useRef, useEffect } from 'react'
import { useDebounceFn, useThrottleFn } from 'ahooks'
import type { Tenant } from '@/types/tenant'

export interface UseTenantPerformanceOptions {
  debounceWait?: number
  throttleWait?: number
  cacheSize?: number
  enableVirtualization?: boolean
  virtualItemHeight?: number
}

/**
 * 租户管理性能优化Hook
 */
export const useTenantPerformance = (
  data: Tenant[],
  options: UseTenantPerformanceOptions = {}
) => {
  const {
    debounceWait = 300,
    throttleWait = 100,
    cacheSize = 100,
    enableVirtualization = false,
    virtualItemHeight = 60
  } = options

  // 缓存引用
  const cacheRef = useRef<Map<string, any>>(new Map())
  const lastDataRef = useRef<Tenant[]>([])

  /**
   * 防抖搜索函数
   */
  const { run: debouncedSearch } = useDebounceFn(
    (searchFn: () => void) => {
      searchFn()
    },
    { wait: debounceWait }
  )

  /**
   * 节流滚动函数
   */
  const { run: throttledScroll } = useThrottleFn(
    (scrollFn: () => void) => {
      scrollFn()
    },
    { wait: throttleWait }
  )

  /**
   * 缓存管理
   */
  const cacheManager = useMemo(() => ({
    get: (key: string) => {
      return cacheRef.current.get(key)
    },
    
    set: (key: string, value: any) => {
      const cache = cacheRef.current
      
      // 如果缓存超过限制，删除最旧的条目
      if (cache.size >= cacheSize) {
        const firstKey = cache.keys().next().value
        cache.delete(firstKey)
      }
      
      cache.set(key, value)
    },
    
    clear: () => {
      cacheRef.current.clear()
    },
    
    has: (key: string) => {
      return cacheRef.current.has(key)
    }
  }), [cacheSize])

  /**
   * 数据变化检测
   */
  const hasDataChanged = useMemo(() => {
    const current = data
    const previous = lastDataRef.current
    
    if (current.length !== previous.length) {
      lastDataRef.current = current
      return true
    }
    
    const changed = current.some((item, index) => {
      const prevItem = previous[index]
      return !prevItem || item.id !== prevItem.id || item.updateTime !== prevItem.updateTime
    })
    
    if (changed) {
      lastDataRef.current = current
    }
    
    return changed
  }, [data])

  /**
   * 虚拟化计算
   */
  const virtualizationHelper = useMemo(() => {
    if (!enableVirtualization) {
      return null
    }

    return {
      getItemHeight: () => virtualItemHeight,
      
      calculateVisibleRange: (
        scrollTop: number,
        containerHeight: number,
        totalItems: number
      ) => {
        const startIndex = Math.floor(scrollTop / virtualItemHeight)
        const endIndex = Math.min(
          startIndex + Math.ceil(containerHeight / virtualItemHeight) + 1,
          totalItems - 1
        )
        
        return { startIndex, endIndex }
      },
      
      getItemOffset: (index: number) => index * virtualItemHeight,
      
      getTotalHeight: (itemCount: number) => itemCount * virtualItemHeight
    }
  }, [enableVirtualization, virtualItemHeight])

  /**
   * 数据分片处理
   */
  const getDataSlice = useCallback((
    startIndex: number,
    endIndex: number
  ): Tenant[] => {
    const cacheKey = `slice_${startIndex}_${endIndex}`
    
    if (cacheManager.has(cacheKey) && !hasDataChanged) {
      return cacheManager.get(cacheKey)
    }
    
    const slice = data.slice(startIndex, endIndex + 1)
    cacheManager.set(cacheKey, slice)
    
    return slice
  }, [data, cacheManager, hasDataChanged])

  /**
   * 优化的搜索函数
   */
  const optimizedSearch = useCallback((
    searchFn: () => void,
    immediate = false
  ) => {
    if (immediate) {
      searchFn()
    } else {
      debouncedSearch(searchFn)
    }
  }, [debouncedSearch])

  /**
   * 优化的滚动处理
   */
  const optimizedScroll = useCallback((
    scrollFn: () => void
  ) => {
    throttledScroll(scrollFn)
  }, [throttledScroll])

  /**
   * 内存使用监控
   */
  const memoryMonitor = useMemo(() => ({
    getCacheSize: () => cacheRef.current.size,
    
    getMemoryUsage: () => {
      if ('memory' in performance) {
        return (performance as any).memory
      }
      return null
    },
    
    clearCache: () => {
      cacheManager.clear()
    }
  }), [cacheManager])

  /**
   * 性能指标收集
   */
  const performanceMetrics = useRef({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0
  })

  // 记录渲染性能
  useEffect(() => {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      const metrics = performanceMetrics.current
      metrics.renderCount++
      metrics.lastRenderTime = renderTime
      metrics.averageRenderTime = 
        (metrics.averageRenderTime * (metrics.renderCount - 1) + renderTime) / metrics.renderCount
    }
  })

  /**
   * 获取性能统计
   */
  const getPerformanceStats = useCallback(() => {
    return {
      ...performanceMetrics.current,
      cacheSize: cacheManager.getCacheSize(),
      dataSize: data.length,
      hasDataChanged
    }
  }, [cacheManager, data.length, hasDataChanged])

  return {
    // 核心功能
    optimizedSearch,
    optimizedScroll,
    getDataSlice,
    
    // 虚拟化
    virtualizationHelper,
    
    // 缓存管理
    cacheManager,
    
    // 性能监控
    memoryMonitor,
    getPerformanceStats,
    
    // 状态
    hasDataChanged
  }
}

/**
 * 租户列表虚拟化Hook
 */
export const useTenantVirtualization = (
  data: Tenant[],
  containerHeight: number,
  itemHeight: number = 60
) => {
  const scrollTopRef = useRef(0)
  
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTopRef.current / itemHeight)
    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const endIndex = Math.min(startIndex + visibleCount + 1, data.length - 1)
    
    return { startIndex, endIndex }
  }, [scrollTopRef.current, containerHeight, itemHeight, data.length])

  const visibleData = useMemo(() => {
    return data.slice(visibleRange.startIndex, visibleRange.endIndex + 1)
  }, [data, visibleRange])

  const totalHeight = data.length * itemHeight
  const offsetY = visibleRange.startIndex * itemHeight

  const handleScroll = useCallback((scrollTop: number) => {
    scrollTopRef.current = scrollTop
  }, [])

  return {
    visibleData,
    visibleRange,
    totalHeight,
    offsetY,
    handleScroll
  }
}
