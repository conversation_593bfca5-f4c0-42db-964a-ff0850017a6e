/**
 * 租户管理Hooks导出文件
 * 
 * 统一导出所有租户管理相关的自定义Hooks
 */

// 基础Hooks
export { useAsyncOperation, useBatchAsyncOperation } from './useAsyncOperation'
export type { AsyncOperationState, AsyncOperationOptions } from './useAsyncOperation'

// 数据管理Hooks
export { useTenantList } from './useTenantList'
export type { UseTenantListOptions, TenantListState } from './useTenantList'

// 操作管理Hooks
export { useTenantOperations } from './useTenantOperations'
export type { UseTenantOperationsOptions } from './useTenantOperations'

// 选择管理Hooks
export { useTenantSelection } from './useTenantSelection'
export type { UseTenantSelectionOptions } from './useTenantSelection'

// 搜索管理Hooks
export { useTenantSearch } from './useTenantSearch'
export type { 
  UseTenantSearchOptions, 
  SearchFormState, 
  SearchStatus 
} from './useTenantSearch'

// 性能优化Hooks
export { 
  useTenantPerformance, 
  useTenantVirtualization 
} from './useTenantPerformance'
export type { UseTenantPerformanceOptions } from './useTenantPerformance'

/**
 * 组合Hook：租户管理完整功能
 * 
 * 将所有相关Hooks组合在一起，提供完整的租户管理功能
 */
export const useTenantManagement = (options: {
  initialPageSize?: number
  autoLoad?: boolean
  enablePerformanceOptimization?: boolean
} = {}) => {
  const {
    initialPageSize = 10,
    autoLoad = true,
    enablePerformanceOptimization = true
  } = options

  // 数据管理
  const tenantList = useTenantList({ initialPageSize, autoLoad })
  
  // 操作管理
  const tenantOperations = useTenantOperations({
    onSuccess: (operation, tenant) => {
      switch (operation) {
        case 'create':
          if (tenant) tenantList.addTenantToList(tenant)
          break
        case 'update':
          if (tenant) tenantList.updateTenantInList(tenant)
          break
        case 'delete':
          if (tenant) tenantList.removeTenantFromList(tenant.id)
          break
        case 'toggleStatus':
          if (tenant) tenantList.updateTenantInList(tenant)
          break
        default:
          tenantList.refresh()
      }
    }
  })
  
  // 选择管理
  const tenantSelection = useTenantSelection()
  
  // 搜索管理
  const tenantSearch = useTenantSearch(tenantList.searchParams, {
    onSearchChange: (params) => {
      tenantList.updateSearchParams(params)
      tenantSelection.clearSelection()
    }
  })
  
  // 性能优化 - 始终调用Hook，但可以通过参数控制是否启用
  const tenantPerformance = useTenantPerformance(
    enablePerformanceOptimization ? tenantList.data : []
  )

  return {
    // 数据状态
    data: tenantList.data,
    total: tenantList.total,
    loading: tenantList.loading || tenantOperations.loading,
    error: tenantList.error,
    
    // 分页和排序
    pagination: tenantList.pagination,
    sortConfig: tenantList.sortConfig,
    handlePageChange: tenantList.handlePageChange,
    handleSort: tenantList.handleSort,
    
    // 搜索功能
    searchState: tenantSearch.formState,
    searchStatus: tenantSearch.getSearchStatus(),
    searchSummary: tenantSearch.getSearchSummary(),
    updateKeyword: tenantSearch.updateKeyword,
    updateAdvancedFilters: tenantSearch.updateAdvancedFilters,
    toggleAdvanced: tenantSearch.toggleAdvanced,
    executeSearch: tenantSearch.executeSearch,
    resetSearch: tenantSearch.resetSearch,
    quickFilterByStatus: tenantSearch.quickFilterByStatus,
    handleKeyPress: tenantSearch.handleKeyPress,
    
    // 选择功能
    selectedIds: tenantSelection.selectedIds,
    selectionStats: tenantSelection.selectionStats,
    toggleSelection: tenantSelection.toggleSelection,
    clearSelection: tenantSelection.clearSelection,
    toggleSelectAll: tenantSelection.toggleSelectAll,
    getSelectedTenants: tenantSelection.getSelectedTenants,
    isSelected: tenantSelection.isSelected,
    isAllSelected: tenantSelection.isAllSelected,
    isIndeterminate: tenantSelection.isIndeterminate,
    bulkOperations: tenantSelection.bulkOperations,
    
    // CRUD操作
    formDialogOpen: tenantOperations.formDialogOpen,
    editingTenant: tenantOperations.editingTenant,
    deleteDialogOpen: tenantOperations.deleteDialogOpen,
    deletingTenant: tenantOperations.deletingTenant,
    createTenant: tenantOperations.createTenant,
    updateTenant: tenantOperations.updateTenant,
    deleteTenant: tenantOperations.deleteTenant,
    batchDeleteTenants: tenantOperations.batchDeleteTenants,
    toggleTenantStatus: tenantOperations.toggleTenantStatus,
    exportTenants: tenantOperations.exportTenants,
    openCreateDialog: tenantOperations.openCreateDialog,
    openEditDialog: tenantOperations.openEditDialog,
    closeFormDialog: tenantOperations.closeFormDialog,
    openDeleteDialog: tenantOperations.openDeleteDialog,
    closeDeleteDialog: tenantOperations.closeDeleteDialog,
    confirmDelete: tenantOperations.confirmDelete,
    
    // 数据操作
    refresh: tenantList.refresh,
    updateTenantInList: tenantList.updateTenantInList,
    removeTenantFromList: tenantList.removeTenantFromList,
    addTenantToList: tenantList.addTenantToList,
    findTenantById: tenantList.findTenantById,
    
    // 性能优化
    performance: tenantPerformance ? {
      optimizedSearch: tenantPerformance.optimizedSearch,
      getPerformanceStats: tenantPerformance.getPerformanceStats,
      hasDataChanged: tenantPerformance.hasDataChanged
    } : null
  }
}
