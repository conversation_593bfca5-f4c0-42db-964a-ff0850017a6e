/**
 * 租户搜索功能Hook
 * 
 * 管理搜索表单的状态和逻辑，包括防抖搜索、高级筛选等
 */

import { useState, useCallback, useEffect } from 'react'
import { useDebounceFn } from 'ahooks'
import type { TenantQueryRequest } from '@/types/tenant'

export interface UseTenantSearchOptions {
  onSearchChange?: (params: TenantQueryRequest) => void
  debounceWait?: number
  autoSearch?: boolean
}

export interface SearchFormState {
  keyword: string
  isAdvancedOpen: boolean
  advancedFilters: Partial<TenantQueryRequest>
}

/**
 * 租户搜索功能Hook
 */
export const useTenantSearch = (
  initialParams: TenantQueryRequest,
  options: UseTenantSearchOptions = {}
) => {
  const { 
    onSearchChange, 
    debounceWait = 800, 
    autoSearch = true 
  } = options

  // 搜索表单状态
  const [formState, setFormState] = useState<SearchFormState>({
    keyword: '',
    isAdvancedOpen: false,
    advancedFilters: {}
  })

  // 当前搜索参数
  const [searchParams, setSearchParams] = useState<TenantQueryRequest>(initialParams)

  /**
   * 防抖搜索函数
   */
  const { run: debouncedSearch } = useDebounceFn(
    (params: TenantQueryRequest) => {
      console.log('🔍 防抖搜索触发:', params)
      setSearchParams(params)
      onSearchChange?.(params)
    },
    { wait: debounceWait }
  )

  /**
   * 更新关键词搜索
   */
  const updateKeyword = useCallback((keyword: string) => {
    setFormState(prev => ({ ...prev, keyword }))
    
    if (autoSearch) {
      const newParams: TenantQueryRequest = {
        ...searchParams,
        keyword: keyword || undefined,
        // 清除具体字段的搜索条件，避免冲突
        tenantCode: undefined,
        tenantName: undefined,
        pageNum: 1 // 搜索时重置到第一页
      }
      debouncedSearch(newParams)
    }
  }, [searchParams, autoSearch, debouncedSearch])

  /**
   * 更新高级筛选
   */
  const updateAdvancedFilters = useCallback((filters: Partial<TenantQueryRequest>) => {
    setFormState(prev => ({
      ...prev,
      advancedFilters: { ...prev.advancedFilters, ...filters }
    }))
  }, [])

  /**
   * 切换高级搜索面板
   */
  const toggleAdvanced = useCallback(() => {
    setFormState(prev => ({ ...prev, isAdvancedOpen: !prev.isAdvancedOpen }))
  }, [])

  /**
   * 执行搜索
   */
  const executeSearch = useCallback(() => {
    const newParams: TenantQueryRequest = {
      ...searchParams,
      ...formState.advancedFilters,
      keyword: formState.keyword || undefined,
      pageNum: 1 // 搜索时重置到第一页
    }

    // 如果有关键词搜索，清除具体字段搜索
    if (formState.keyword) {
      newParams.tenantCode = undefined
      newParams.tenantName = undefined
    }

    console.log('🔍 执行搜索:', newParams)
    setSearchParams(newParams)
    onSearchChange?.(newParams)
  }, [searchParams, formState, onSearchChange])

  /**
   * 重置搜索
   */
  const resetSearch = useCallback(() => {
    const resetParams: TenantQueryRequest = {
      pageNum: 1,
      pageSize: searchParams.pageSize
    }

    setFormState({
      keyword: '',
      isAdvancedOpen: false,
      advancedFilters: {}
    })

    console.log('🔄 重置搜索:', resetParams)
    setSearchParams(resetParams)
    onSearchChange?.(resetParams)
  }, [searchParams.pageSize, onSearchChange])

  /**
   * 快速筛选 - 按状态
   */
  const quickFilterByStatus = useCallback((status?: number) => {
    const newParams: TenantQueryRequest = {
      ...searchParams,
      status,
      pageNum: 1
    }

    setFormState(prev => ({
      ...prev,
      advancedFilters: { ...prev.advancedFilters, status }
    }))

    console.log('⚡ 快速筛选 - 状态:', status)
    setSearchParams(newParams)
    onSearchChange?.(newParams)
  }, [searchParams, onSearchChange])

  /**
   * 快速筛选 - 按时间范围
   */
  const quickFilterByTimeRange = useCallback((days: number) => {
    const endTime = new Date().toISOString()
    const startTime = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()

    const newParams: TenantQueryRequest = {
      ...searchParams,
      startTime,
      endTime,
      pageNum: 1
    }

    setFormState(prev => ({
      ...prev,
      advancedFilters: { ...prev.advancedFilters, startTime, endTime }
    }))

    console.log('⚡ 快速筛选 - 时间范围:', { startTime, endTime })
    setSearchParams(newParams)
    onSearchChange?.(newParams)
  }, [searchParams, onSearchChange])

  /**
   * 处理键盘事件
   */
  const handleKeyPress = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      executeSearch()
    }
  }, [executeSearch])

  /**
   * 获取搜索状态
   */
  const getSearchStatus = useCallback(() => {
    const hasKeyword = !!formState.keyword
    const hasAdvancedFilters = Object.keys(formState.advancedFilters).some(
      key => formState.advancedFilters[key as keyof TenantQueryRequest] !== undefined
    )
    
    return {
      hasKeyword,
      hasAdvancedFilters,
      hasAnyFilter: hasKeyword || hasAdvancedFilters,
      isAdvancedOpen: formState.isAdvancedOpen
    }
  }, [formState])

  /**
   * 构建搜索摘要
   */
  const getSearchSummary = useCallback(() => {
    const summary: string[] = []
    
    if (formState.keyword) {
      summary.push(`关键词: "${formState.keyword}"`)
    }
    
    if (formState.advancedFilters.status !== undefined) {
      const statusText = formState.advancedFilters.status === 1 ? '启用' : '禁用'
      summary.push(`状态: ${statusText}`)
    }
    
    if (formState.advancedFilters.tenantCode) {
      summary.push(`租户编码: "${formState.advancedFilters.tenantCode}"`)
    }
    
    if (formState.advancedFilters.tenantName) {
      summary.push(`租户名称: "${formState.advancedFilters.tenantName}"`)
    }
    
    if (formState.advancedFilters.contactName) {
      summary.push(`联系人: "${formState.advancedFilters.contactName}"`)
    }
    
    if (formState.advancedFilters.startTime && formState.advancedFilters.endTime) {
      summary.push(`创建时间: ${formState.advancedFilters.startTime} ~ ${formState.advancedFilters.endTime}`)
    }
    
    return summary
  }, [formState])

  // 同步外部参数变化
  useEffect(() => {
    setSearchParams(initialParams)
  }, [initialParams])

  return {
    // 状态
    formState,
    searchParams,
    
    // 基础操作
    updateKeyword,
    updateAdvancedFilters,
    toggleAdvanced,
    executeSearch,
    resetSearch,
    
    // 快速筛选
    quickFilterByStatus,
    quickFilterByTimeRange,
    
    // 事件处理
    handleKeyPress,
    
    // 状态查询
    getSearchStatus,
    getSearchSummary
  }
}
