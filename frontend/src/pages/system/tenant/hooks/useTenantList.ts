/**
 * 租户列表数据管理Hook
 * 
 * 管理租户列表的数据获取、分页、排序等功能
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import type { Tenant, TenantQueryRequest } from '@/types/tenant'
import type { PageResult } from '@/types'
import { TenantService } from '@/services/tenant'
import { useAsyncOperation } from './useAsyncOperation'

export interface UseTenantListOptions {
  initialPageSize?: number
  autoLoad?: boolean
}

export interface TenantListState {
  data: Tenant[]
  total: number
  searchParams: TenantQueryRequest
  sortConfig: {
    field: string
    order: 'asc' | 'desc'
  }
}

/**
 * 租户列表数据管理Hook
 */
export const useTenantList = (options: UseTenantListOptions = {}) => {
  const { initialPageSize = 10, autoLoad = true } = options
  
  const { loading, error, execute } = useAsyncOperation()
  
  // 列表状态
  const [state, setState] = useState<TenantListState>({
    data: [],
    total: 0,
    searchParams: {
      pageNum: 1,
      pageSize: initialPageSize
    },
    sortConfig: {
      field: 'createTime',
      order: 'desc'
    }
  })

  /**
   * 加载租户列表
   */
  const loadTenants = useCallback(async (params?: Partial<TenantQueryRequest>) => {
    const queryParams = params ? { ...state.searchParams, ...params } : state.searchParams
    
    console.log('🔄 开始加载租户列表:', queryParams)
    
    const result = await execute(
      () => TenantService.pageTenants(queryParams),
      {
        errorMessage: '加载租户列表失败',
        showSuccessToast: false
      }
    )
    
    if (result) {
      setState(prev => ({
        ...prev,
        data: result.records,
        total: result.total,
        searchParams: queryParams
      }))
      console.log('✅ 租户列表加载成功:', result)
    }
    
    return result
  }, [state.searchParams, execute])

  /**
   * 刷新列表
   */
  const refresh = useCallback(() => {
    return loadTenants()
  }, [loadTenants])

  /**
   * 更新搜索参数
   */
  const updateSearchParams = useCallback((params: Partial<TenantQueryRequest>) => {
    setState(prev => ({
      ...prev,
      searchParams: { ...prev.searchParams, ...params }
    }))
  }, [])

  /**
   * 重置搜索参数
   */
  const resetSearchParams = useCallback(() => {
    setState(prev => ({
      ...prev,
      searchParams: {
        pageNum: 1,
        pageSize: prev.searchParams.pageSize
      }
    }))
  }, [])

  /**
   * 处理分页变化
   */
  const handlePageChange = useCallback((pageNum: number, pageSize?: number) => {
    const newParams: Partial<TenantQueryRequest> = { pageNum }
    if (pageSize && pageSize !== state.searchParams.pageSize) {
      newParams.pageSize = pageSize
    }
    updateSearchParams(newParams)
  }, [state.searchParams.pageSize, updateSearchParams])

  /**
   * 处理排序变化
   */
  const handleSort = useCallback((field: string) => {
    setState(prev => {
      const newOrder = prev.sortConfig.field === field && prev.sortConfig.order === 'asc' 
        ? 'desc' 
        : 'asc'
      
      return {
        ...prev,
        sortConfig: { field, order: newOrder }
      }
    })
  }, [])

  /**
   * 根据ID查找租户
   */
  const findTenantById = useCallback((id: number): Tenant | undefined => {
    return state.data.find(tenant => tenant.id === id)
  }, [state.data])

  /**
   * 更新列表中的租户数据
   */
  const updateTenantInList = useCallback((updatedTenant: Tenant) => {
    setState(prev => ({
      ...prev,
      data: prev.data.map(tenant => 
        tenant.id === updatedTenant.id ? updatedTenant : tenant
      )
    }))
  }, [])

  /**
   * 从列表中移除租户
   */
  const removeTenantFromList = useCallback((tenantId: number) => {
    setState(prev => ({
      ...prev,
      data: prev.data.filter(tenant => tenant.id !== tenantId),
      total: prev.total - 1
    }))
  }, [])

  /**
   * 添加租户到列表
   */
  const addTenantToList = useCallback((newTenant: Tenant) => {
    setState(prev => ({
      ...prev,
      data: [newTenant, ...prev.data],
      total: prev.total + 1
    }))
  }, [])

  // 计算分页信息
  const pagination = useMemo(() => ({
    current: state.searchParams.pageNum || 1,
    pageSize: state.searchParams.pageSize || initialPageSize,
    total: state.total,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => 
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
  }), [state.searchParams.pageNum, state.searchParams.pageSize, state.total, initialPageSize])

  // 当搜索参数变化时自动加载数据
  useEffect(() => {
    if (autoLoad) {
      const queryParams = state.searchParams

      const loadData = async () => {
        console.log('🔄 开始加载租户列表:', queryParams)

        const result = await execute(
          () => TenantService.pageTenants(queryParams),
          {
            errorMessage: '加载租户列表失败',
            showSuccessToast: false
          }
        )

        if (result) {
          setState(prev => ({
            ...prev,
            data: result.records,
            total: result.total
          }))
          console.log('✅ 租户列表加载成功:', result)
        }
      }

      loadData()
    }
  }, [state.searchParams, autoLoad, execute])

  // 初始加载
  useEffect(() => {
    if (autoLoad && state.data.length === 0) {
      const initialLoad = async () => {
        const result = await execute(
          () => TenantService.pageTenants(state.searchParams),
          {
            errorMessage: '加载租户列表失败',
            showSuccessToast: false
          }
        )

        if (result) {
          setState(prev => ({
            ...prev,
            data: result.records,
            total: result.total
          }))
        }
      }

      initialLoad()
    }
  }, []) // 只在组件挂载时执行一次

  return {
    // 状态
    data: state.data,
    total: state.total,
    loading,
    error,
    searchParams: state.searchParams,
    sortConfig: state.sortConfig,
    pagination,
    
    // 操作方法
    loadTenants,
    refresh,
    updateSearchParams,
    resetSearchParams,
    handlePageChange,
    handleSort,
    findTenantById,
    updateTenantInList,
    removeTenantFromList,
    addTenantToList
  }
}
