/**
 * 租户管理模块主导出文件
 * 
 * 导出重构后的租户管理模块的主要组件和功能
 */

// 主组件导出
export { default as TenantList } from './TenantList'
export { default } from './TenantList'

// 子组件导出
export { default as TenantSearchForm } from './components/TenantSearchForm'
export { default as TenantTable } from './components/TenantTable'
export { default as TenantForm } from './components/TenantForm'
export { default as TenantStatsDialog } from './components/TenantStatsDialog'
export { default as TenantPageHeader } from './components/TenantPageHeader'
export { default as TenantBulkActions } from './components/TenantBulkActions'
export { default as TenantPagination } from './components/TenantPagination'
export { default as TenantErrorBoundary } from './components/TenantErrorBoundary'

// Hooks导出
export * from './hooks'

// 类型导出
export type {
  AsyncOperationState,
  AsyncOperationOptions,
  UseTenantListOptions,
  TenantListState,
  UseTenantOperationsOptions,
  UseTenantSelectionOptions,
  UseTenantSearchOptions,
  SearchFormState,
  SearchStatus,
  UseTenantPerformanceOptions
} from './hooks'
