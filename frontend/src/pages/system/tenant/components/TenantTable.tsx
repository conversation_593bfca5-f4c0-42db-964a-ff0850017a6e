/**
 * 租户表格组件
 * 
 * 提供租户列表的表格展示，包括排序、选择、操作等功能
 */

import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Badge,
  Checkbox,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  Skeleton
} from '@/components/ui'
import { 
  Edit, 
  Trash2, 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown,
  Users,
  Settings,
  BarChart3,
  Building2
} from 'lucide-react'
import type { Tenant } from '@/types/tenant.ts'
import { getTenantStatusLabel } from '@/types/tenant.ts'
import { ActionPermissionButton } from '@/components/auth/PermissionWrapper'
import { PERMISSIONS } from '@/constants'

export interface TenantTableProps {
  data: Tenant[]
  loading?: boolean
  selectedIds: number[]
  onSelectionChange: (ids: number[]) => void
  onSelectAll?: () => void
  isAllSelected?: boolean
  isIndeterminate?: boolean
  onEdit: (tenant: Tenant) => void
  onDelete: (tenant: Tenant) => void
  onToggleStatus: (tenant: Tenant) => void
  onManageUsers: (tenant: Tenant) => void
  onManageConfigs: (tenant: Tenant) => void
  onViewStats: (tenant: Tenant) => void
  sortConfig?: {
    field: string
    order: 'asc' | 'desc'
  }
  onSort?: (field: string) => void
}

/**
 * 租户表格组件
 */
const TenantTable: React.FC<TenantTableProps> = ({
  data,
  loading = false,
  selectedIds,
  onSelectionChange,
  onSelectAll,
  isAllSelected = false,
  isIndeterminate = false,
  onEdit,
  onDelete,
  onToggleStatus,
  onManageUsers,
  onManageConfigs,
  onViewStats,
  sortConfig,
  onSort
}) => {
  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (onSelectAll) {
      onSelectAll()
    } else {
      // 兼容旧的选择逻辑
      if (checked) {
        onSelectionChange(data.map(tenant => tenant.id))
      } else {
        onSelectionChange([])
      }
    }
  }

  // 处理单行选择
  const handleSelectRow = (tenantId: number, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, tenantId])
    } else {
      onSelectionChange(selectedIds.filter(id => id !== tenantId))
    }
  }

  // 渲染排序图标
  const renderSortIcon = (field: string) => {
    if (!onSort) return null
    
    if (sortConfig?.field === field) {
      return sortConfig.order === 'asc' ? (
        <ArrowUp className="w-4 h-4" />
      ) : (
        <ArrowDown className="w-4 h-4" />
      )
    }
    return <ArrowUpDown className="w-4 h-4" />
  }

  // 处理排序点击
  const handleSort = (field: string) => {
    if (onSort) {
      onSort(field)
    }
  }

  // 简单的日期格式化函数
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 格式化存储空间
  const formatStorage = (mb?: number) => {
    if (!mb) return '-'
    if (mb < 1024) return `${mb}MB`
    return `${(mb / 1024).toFixed(1)}GB`
  }

  // 如果没有数据，显示空状态
  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center">
            <Building2 className="w-8 h-8 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            <div className="text-lg font-medium text-muted-foreground">暂无租户数据</div>
            <div className="text-sm text-muted-foreground">
              没有找到符合条件的租户，请尝试调整搜索条件
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                ref={(el) => {
                  if (el) el.indeterminate = isIndeterminate
                }}
                onCheckedChange={handleSelectAll}
                disabled={loading}
              />
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => handleSort('tenantCode')}
            >
              <div className="flex items-center space-x-2">
                <span>租户编码</span>
                {renderSortIcon('tenantCode')}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => handleSort('tenantName')}
            >
              <div className="flex items-center space-x-2">
                <span>租户名称</span>
                {renderSortIcon('tenantName')}
              </div>
            </TableHead>
            <TableHead>联系人</TableHead>
            <TableHead>联系方式</TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => handleSort('status')}
            >
              <div className="flex items-center space-x-2">
                <span>状态</span>
                {renderSortIcon('status')}
              </div>
            </TableHead>
            <TableHead>限制配置</TableHead>
            <TableHead>过期时间</TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => handleSort('createTime')}
            >
              <div className="flex items-center space-x-2">
                <span>创建时间</span>
                {renderSortIcon('createTime')}
              </div>
            </TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            // 加载骨架屏
            Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell><Skeleton className="h-4 w-4" /></TableCell>
                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
              </TableRow>
            ))
          ) : (
            data.map((tenant) => (
              <TableRow key={tenant.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedIds.includes(tenant.id)}
                    onCheckedChange={(checked) => handleSelectRow(tenant.id, checked as boolean)}
                  />
                </TableCell>
                <TableCell className="font-medium">{tenant.tenantCode}</TableCell>
                <TableCell>{tenant.tenantName}</TableCell>
                <TableCell>{tenant.contactName || '-'}</TableCell>
                <TableCell>
                  <div className="space-y-1">
                    {tenant.contactPhone && (
                      <div className="text-sm">{tenant.contactPhone}</div>
                    )}
                    {tenant.contactEmail && (
                      <div className="text-sm text-muted-foreground">{tenant.contactEmail}</div>
                    )}
                    {!tenant.contactPhone && !tenant.contactEmail && '-'}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={tenant.status === 1 ? 'default' : 'secondary'}>
                    {getTenantStatusLabel(tenant.status)}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="space-y-1 text-sm">
                    {tenant.maxUserCount && (
                      <div>用户: {tenant.maxUserCount}</div>
                    )}
                    {!tenant.maxUserCount && '-'}
                  </div>
                </TableCell>
                <TableCell>
                  {tenant.expireTime ? formatDateTime(tenant.expireTime) : '永久'}
                </TableCell>
                <TableCell>{formatDateTime(tenant.createTime)}</TableCell>
                <TableCell>
                  <div className="flex items-center justify-end gap-1">
                    {/* 查看统计按钮 - 蓝色 */}
                    <ActionPermissionButton
                      module="tenant"
                      action="view"
                      config={{
                        text: '',
                        icon: BarChart3,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onViewStats(tenant),
                        title: '查看统计',
                        className: 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                      }}
                    />

                    {/* 管理用户按钮 - 紫色 */}
                    <ActionPermissionButton
                      module="tenant"
                      action="edit"
                      config={{
                        text: '',
                        icon: Users,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onManageUsers(tenant),
                        title: '管理用户',
                        className: 'text-purple-600 hover:text-purple-700 hover:bg-purple-50'
                      }}
                    />

                    {/* 管理配置按钮 - 橙色 */}
                    <ActionPermissionButton
                      module="tenant"
                      action="edit"
                      config={{
                        text: '',
                        icon: Settings,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onManageConfigs(tenant),
                        title: '管理配置',
                        className: 'text-orange-600 hover:text-orange-700 hover:bg-orange-50'
                      }}
                    />

                    {/* 编辑按钮 - 蓝色 */}
                    <ActionPermissionButton
                      module="tenant"
                      action="edit"
                      config={{
                        text: '',
                        icon: Edit,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onEdit(tenant),
                        title: '编辑租户',
                        className: 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                      }}
                    />

                    {/* 状态切换按钮 - 动态颜色 */}
                    <ActionPermissionButton
                      module="tenant"
                      action="edit"
                      config={{
                        text: tenant.status === 1 ? '禁用' : '启用',
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onToggleStatus(tenant),
                        title: tenant.status === 1 ? '禁用租户' : '启用租户',
                        className: tenant.status === 1
                          ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                          : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                      }}
                    />

                    {/* 删除按钮 - 红色 */}
                    <ActionPermissionButton
                      module="tenant"
                      action="delete"
                      config={{
                        text: '',
                        icon: Trash2,
                        variant: 'ghost',
                        size: 'sm',
                        onClick: () => onDelete(tenant),
                        title: '删除租户',
                        className: 'text-red-600 hover:text-red-700 hover:bg-red-50'
                      }}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}

export default TenantTable
