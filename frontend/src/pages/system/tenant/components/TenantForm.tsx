/**
 * 租户表单组件
 * 
 * 提供租户的新增和编辑功能
 */

import React, { useEffect, useState } from 'react'
import {
  Button,
  Input,
  Textarea,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '../../../../components/ui'
import { Loader2, Building2 } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import type { Tenant, TenantCreateRequest, TenantUpdateRequest } from '../../../../types/tenant'
import { TENANT_STATUS_OPTIONS } from '../../../../types/tenant'
import { TenantService } from '../../../../services/tenant'

// 表单验证模式
const tenantFormSchema = z.object({
  tenantCode: z.string()
    .min(1, '租户编码不能为空')
    .max(50, '租户编码不能超过50个字符')
    .regex(/^[A-Z0-9_]+$/, '租户编码只能包含大写字母、数字和下划线'),
  tenantName: z.string()
    .min(1, '租户名称不能为空')
    .max(100, '租户名称不能超过100个字符'),
  contactName: z.string()
    .max(50, '联系人不能超过50个字符')
    .optional(),
  contactPhone: z.string()
    .regex(/^$|^1[3-9]\d{9}$/, '请输入正确的手机号码')
    .optional(),
  contactEmail: z.string()
    .regex(/^$|^[^\s@]+@[^\s@]+\.[^\s@]+$/, '请输入正确的邮箱地址')
    .optional(),
  status: z.number(),
  expireTime: z.string().optional(),
  maxUserCount: z.number()
    .min(1, '最大用户数量不能小于1')
    .max(10000, '最大用户数量不能超过10000')
    .optional(),
  remark: z.string()
    .max(500, '备注不能超过500个字符')
    .optional()
})

type TenantFormData = z.infer<typeof tenantFormSchema>

export interface TenantFormProps {
  tenant: Tenant | null
  onSubmit: (data: TenantCreateRequest | TenantUpdateRequest) => Promise<Tenant | null>
  onCancel: () => void
  loading?: boolean
}

/**
 * 租户表单组件
 */
const TenantForm: React.FC<TenantFormProps> = ({
  tenant,
  onSubmit,
  onCancel,
  loading = false
}) => {
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [codeChecking, setCodeChecking] = useState(false)

  const isEdit = !!tenant
  const title = isEdit ? '编辑租户' : '新增租户'

  // 表单实例
  const form = useForm<TenantFormData>({
    resolver: zodResolver(tenantFormSchema),
    defaultValues: {
      tenantCode: '',
      tenantName: '',
      contactName: '',
      contactPhone: '',
      contactEmail: '',
      status: 1,
      expireTime: '',
      maxUserCount: undefined,
      remark: ''
    }
  })

  // 当对话框打开时，初始化表单数据
  useEffect(() => {
    if (open) {
      setSubmitError(null)
      if (tenant) {
        // 编辑模式：填充现有数据
        form.reset({
          tenantCode: tenant.tenantCode,
          tenantName: tenant.tenantName,
          contactName: tenant.contactName || '',
          contactPhone: tenant.contactPhone || '',
          contactEmail: tenant.contactEmail || '',
          status: tenant.status,
          expireTime: tenant.expireTime || '',
          maxUserCount: tenant.maxUserCount,
          remark: tenant.remark || ''
        })
      } else {
        // 新增模式：重置为默认值
        form.reset({
          tenantCode: '',
          tenantName: '',
          contactName: '',
          contactPhone: '',
          contactEmail: '',
          status: 1,
          expireTime: '',
          maxUserCount: undefined,
          remark: ''
        })
      }
    }
  }, [open, tenant, form])

  // 检查租户编码是否存在
  const checkTenantCode = async (tenantCode: string) => {
    if (!tenantCode || tenantCode === tenant?.tenantCode) return

    try {
      setCodeChecking(true)
      const exists = await TenantService.checkTenantCodeExists(tenantCode, tenant?.id)
      if (exists) {
        form.setError('tenantCode', {
          type: 'manual',
          message: '租户编码已存在'
        })
      }
    } catch (error) {
      console.error('检查租户编码失败:', error)
    } finally {
      setCodeChecking(false)
    }
  }

  // 处理表单提交
  const handleSubmit = async (data: TenantFormData) => {
    try {
      setSubmitError(null)

      console.log('🚀 提交租户表单:', { isEdit, data })

      let submitData: TenantCreateRequest | TenantUpdateRequest

      if (isEdit && tenant) {
        // 编辑租户
        submitData = {
          id: tenant.id,
          tenantName: data.tenantName,
          contactName: data.contactName || undefined,
          contactPhone: data.contactPhone || undefined,
          contactEmail: data.contactEmail || undefined,
          status: data.status,
          expireTime: data.expireTime || undefined,
          maxUserCount: data.maxUserCount,
          remark: data.remark || undefined
        } as TenantUpdateRequest
      } else {
        // 新增租户
        submitData = {
          tenantCode: data.tenantCode,
          tenantName: data.tenantName,
          contactName: data.contactName || undefined,
          contactPhone: data.contactPhone || undefined,
          contactEmail: data.contactEmail || undefined,
          status: data.status,
          expireTime: data.expireTime || undefined,
          maxUserCount: data.maxUserCount,
          remark: data.remark || undefined
        } as TenantCreateRequest
      }

      // 调用父组件的提交处理函数
      const result = await onSubmit(submitData)

      if (result) {
        console.log('✅ 租户表单提交成功')
        // 成功后重置表单
        form.reset()
      }
    } catch (error) {
      console.error('❌ 租户表单提交失败:', error)
      setSubmitError((error as Error).message)
    }
  }

  return (
    <div className="space-y-6">
      {submitError && (
        <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
          {submitError}
        </div>
      )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 租户编码 */}
              <FormField
                control={form.control}
                name="tenantCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>租户编码 *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入租户编码"
                        disabled={isEdit || loading} // 编辑时不允许修改编码
                        value={field.value}
                        onChange={(e) => {
                          const upperValue = e.target.value.toUpperCase()
                          field.onChange(upperValue)
                          console.log('🔤 租户编码自动转大写:', e.target.value, '→', upperValue)
                        }}
                        onBlur={() => checkTenantCode(field.value)}
                      />
                    </FormControl>
                    <FormDescription>
                      {isEdit ? '编辑时不允许修改租户编码' : '租户编码将自动转换为大写，只能包含字母、数字和下划线'}
                    </FormDescription>
                    <FormMessage />
                    {codeChecking && (
                      <div className="text-sm text-muted-foreground">
                        <Loader2 className="w-3 h-3 animate-spin inline mr-1" />
                        检查编码是否可用...
                      </div>
                    )}
                  </FormItem>
                )}
              />

              {/* 租户名称 */}
              <FormField
                control={form.control}
                name="tenantName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>租户名称 *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入租户名称"
                        disabled={loading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 联系人 */}
              <FormField
                control={form.control}
                name="contactName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>联系人</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入联系人"
                        disabled={loading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 联系电话 */}
              <FormField
                control={form.control}
                name="contactPhone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>联系电话</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入联系电话"
                        disabled={loading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 联系邮箱 */}
              <FormField
                control={form.control}
                name="contactEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>联系邮箱</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入联系邮箱"
                        disabled={loading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 租户状态 */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>租户状态 *</FormLabel>
                    <Select
                      value={field.value.toString()}
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      disabled={loading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择状态" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {TENANT_STATUS_OPTIONS.map(option => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 过期时间 */}
              <FormField
                control={form.control}
                name="expireTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>过期时间</FormLabel>
                    <FormControl>
                      <Input
                        type="datetime-local"
                        value={field.value ? field.value.slice(0, 16) : ''}
                        onChange={(e) => field.onChange(e.target.value ? `${e.target.value}:00` : '')}
                        placeholder="选择过期时间"
                        disabled={loading}
                      />
                    </FormControl>
                    <FormDescription>
                      不设置则为永久有效
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 最大用户数量 */}
              <FormField
                control={form.control}
                name="maxUserCount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>最大用户数量</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="请输入最大用户数量"
                        disabled={loading}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormDescription>
                      不设置则默认100个用户
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 备注 */}
            <FormField
              control={form.control}
              name="remark"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请输入备注信息"
                      disabled={loading}
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEdit ? '更新' : '创建'}
              </Button>
            </div>
          </form>
        </Form>
    </div>
  )
}

export default TenantForm
