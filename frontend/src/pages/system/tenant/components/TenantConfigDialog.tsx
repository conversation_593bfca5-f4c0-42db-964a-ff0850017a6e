/**
 * 租户配置管理对话框组件
 * 
 * 提供租户配置的查看和编辑功能
 */

import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Button,
  Input,
  Textarea,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Badge,
  Separator
} from '@/components/ui'
import { Settings, Save, RotateCcw, AlertCircle } from 'lucide-react'
import { useToast } from '@/hooks'
import type { Tenant } from '@/types/tenant'
import { TenantService } from '@/services/tenant'

export interface TenantConfig {
  id?: number
  tenantId: number
  configKey: string
  configValue: string
  configName: string
  configDesc?: string
  configType: number
  isSystem: number
  sortOrder: number
  status: number
}

export interface TenantConfigDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  tenant: Tenant | null
  onSuccess?: () => void
}

/**
 * 租户配置管理对话框组件
 */
const TenantConfigDialog: React.FC<TenantConfigDialogProps> = ({
  open,
  onOpenChange,
  tenant,
  onSuccess
}) => {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [configs, setConfigs] = useState<TenantConfig[]>([])
  const [hasChanges, setHasChanges] = useState(false)

  // 加载租户配置
  const loadConfigs = async () => {
    if (!tenant) return

    try {
      setLoading(true)
      const configs = await TenantService.getTenantConfigs(tenant.id)
      setConfigs(configs || [])
    } catch (error) {
      console.error('加载租户配置失败:', error)
      toast({
        title: '加载失败',
        description: '加载租户配置失败: ' + (error as Error).message,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // 保存配置
  const saveConfigs = async () => {
    if (!tenant) return

    try {
      setLoading(true)
      await TenantService.updateTenantConfigs(tenant.id, configs)

      toast({
        title: '保存成功',
        description: '租户配置保存成功'
      })
      setHasChanges(false)
      onSuccess?.()
    } catch (error) {
      console.error('保存租户配置失败:', error)
      toast({
        title: '保存失败',
        description: '保存租户配置失败: ' + (error as Error).message,
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // 更新配置值
  const updateConfigValue = (configKey: string, value: string) => {
    setConfigs(prev => prev.map(config => 
      config.configKey === configKey 
        ? { ...config, configValue: value }
        : config
    ))
    setHasChanges(true)
  }

  // 切换配置状态
  const toggleConfigStatus = (configKey: string) => {
    setConfigs(prev => prev.map(config => 
      config.configKey === configKey 
        ? { ...config, status: config.status === 1 ? 0 : 1 }
        : config
    ))
    setHasChanges(true)
  }

  // 重置配置
  const resetConfigs = () => {
    loadConfigs()
    setHasChanges(false)
  }

  // 当对话框打开时加载配置
  useEffect(() => {
    if (open && tenant) {
      loadConfigs()
      setHasChanges(false)
    }
  }, [open, tenant])

  // 渲染配置项
  const renderConfigItem = (config: TenantConfig) => {
    const isBoolean = ['true', 'false'].includes(config.configValue.toLowerCase())
    const isNumber = /^\d+$/.test(config.configValue)

    return (
      <Card key={config.configKey} className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CardTitle className="text-sm font-medium">
                {config.configName}
              </CardTitle>
              {config.isSystem === 1 && (
                <Badge variant="secondary" className="text-xs">
                  系统配置
                </Badge>
              )}
            </div>
            <Switch
              checked={config.status === 1}
              onCheckedChange={() => toggleConfigStatus(config.configKey)}
              disabled={loading}
            />
          </div>
          {config.configDesc && (
            <p className="text-xs text-muted-foreground">
              {config.configDesc}
            </p>
          )}
        </CardHeader>
        <CardContent className="pt-0">
          {isBoolean ? (
            <Select
              value={config.configValue}
              onValueChange={(value) => updateConfigValue(config.configKey, value)}
              disabled={loading || config.status === 0}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">是</SelectItem>
                <SelectItem value="false">否</SelectItem>
              </SelectContent>
            </Select>
          ) : isNumber ? (
            <Input
              type="number"
              value={config.configValue}
              onChange={(e) => updateConfigValue(config.configKey, e.target.value)}
              disabled={loading || config.status === 0}
              placeholder="请输入数值"
            />
          ) : config.configValue.length > 50 ? (
            <Textarea
              value={config.configValue}
              onChange={(e) => updateConfigValue(config.configKey, e.target.value)}
              disabled={loading || config.status === 0}
              placeholder="请输入配置值"
              rows={3}
            />
          ) : (
            <Input
              value={config.configValue}
              onChange={(e) => updateConfigValue(config.configKey, e.target.value)}
              disabled={loading || config.status === 0}
              placeholder="请输入配置值"
            />
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="min-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            租户配置管理
          </DialogTitle>
          <DialogDescription>
            管理租户 "{tenant?.tenantName}" 的系统配置和自定义配置
          </DialogDescription>
        </DialogHeader>

        {hasChanges && (
          <div className="flex items-center space-x-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
            <AlertCircle className="w-4 h-4 text-amber-600" />
            <span className="text-sm text-amber-800">
              您有未保存的更改，请记得保存配置
            </span>
          </div>
        )}

        <div className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-muted-foreground">加载配置中...</div>
            </div>
          ) : configs.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-muted-foreground">暂无配置数据</div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {configs
                .sort((a, b) => a.sortOrder - b.sortOrder)
                .map(renderConfigItem)}
            </div>
          )}
        </div>

        <Separator />

        <DialogFooter>
          <Button
            variant="outline"
            onClick={resetConfigs}
            disabled={loading || !hasChanges}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            重置
          </Button>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            onClick={saveConfigs}
            disabled={loading || !hasChanges}
          >
            <Save className="w-4 h-4 mr-2" />
            保存配置
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default TenantConfigDialog
