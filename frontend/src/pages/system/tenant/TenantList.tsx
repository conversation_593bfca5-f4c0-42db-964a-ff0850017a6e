/**
 * 租户管理主页面 - 重构版本
 *
 * 使用自定义Hooks重构，提高代码复用性和可维护性
 * 统一重构版本 - 完整的权限控制实现
 */

import React, { useCallback, useState } from 'react'
import {
  Card,
  CardContent,
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui'
import { Plus, Download, Trash2, RefreshCw, Upload, Search } from 'lucide-react'
import { PagePermissionWrapper, PermissionToolbar } from '@/components/auth/PermissionWrapper'
import type { Tenant } from '@/types/tenant'

// 组件导入
import TenantSearchForm from './components/TenantSearchForm'
import TenantTable from './components/TenantTable'
import TenantForm from './components/TenantForm'
import TenantStatsDialog from './components/TenantStatsDialog'
import TenantConfigDialog from './components/TenantConfigDialog'
import TenantUserDialog from './components/TenantUserDialog'
import TenantPagination from './components/TenantPagination'
import TenantErrorBoundary from './components/TenantErrorBoundary'

// 自定义Hooks导入
import { useTenantList } from './hooks/useTenantList'
import { useTenantOperations } from './hooks/useTenantOperations'
import { useTenantSelection } from './hooks/useTenantSelection'
import { useTenantSearch } from './hooks/useTenantSearch'
/**
 * 租户管理主页面
 */
const TenantList: React.FC = () => {
  // 对话框状态
  const [statsDialogOpen, setStatsDialogOpen] = useState(false)
  const [statsViewingTenant, setStatsViewingTenant] = useState<Tenant | null>(null)
  const [configDialogOpen, setConfigDialogOpen] = useState(false)
  const [configManagingTenant, setConfigManagingTenant] = useState<Tenant | null>(null)
  const [userDialogOpen, setUserDialogOpen] = useState(false)
  const [userManagingTenant, setUserManagingTenant] = useState<Tenant | null>(null)

  // 使用租户列表Hook
  const {
    data: tenants,
    total,
    loading,
    searchParams,
    sortConfig,
    pagination,
    handlePageChange,
    handleSort,
    updateTenantInList,
    removeTenantFromList,
    addTenantToList,
    refresh,
    updateSearchParams
  } = useTenantList()

  // 使用租户操作Hook
  const {
    loading: operationLoading,
    formDialogOpen,
    editingTenant,
    deleteDialogOpen,
    deletingTenant,
    createTenant,
    updateTenant,
    toggleTenantStatus,
    exportTenants,
    batchDeleteTenants,
    openCreateDialog,
    openEditDialog,
    closeFormDialog,
    openDeleteDialog,
    closeDeleteDialog,
    confirmDelete
  } = useTenantOperations({
    onSuccess: (operation, tenant) => {
      switch (operation) {
        case 'create':
          if (tenant) addTenantToList(tenant)
          break
        case 'update':
          if (tenant) updateTenantInList(tenant)
          break
        case 'delete':
          if (tenant) removeTenantFromList(tenant.id)
          break
        case 'toggleStatus':
          if (tenant) updateTenantInList(tenant)
          break
        case 'batchDelete':
          refresh()
          clearSelection()
          break
        default:
          refresh()
      }
    }
  })

  // 使用选择Hook
  const {
    selectedIds,
    selectionStats,
    setSelection,
    clearSelection,
    toggleSelectAll,
    getSelectedTenants,
    isAllSelected,
    isIndeterminate,
  } = useTenantSelection()

  // 使用搜索Hook
  const {
    formState,
    updateKeyword,
    updateAdvancedFilters,
    toggleAdvanced,
    executeSearch,
    resetSearch,
    quickFilterByStatus,
    handleKeyPress,
    getSearchStatus,
    getSearchSummary
  } = useTenantSearch(searchParams, {
    onSearchChange: (params) => {
      updateSearchParams(params)
      clearSelection() // 搜索时清除选择
    }
  })

  // 处理统计查看
  const handleViewStats = useCallback((tenant: Tenant) => {
    setStatsViewingTenant(tenant)
    setStatsDialogOpen(true)
  }, [])

  // 处理用户管理
  const handleManageUsers = useCallback((tenant: Tenant) => {
    setUserManagingTenant(tenant)
    setUserDialogOpen(true)
  }, [])

  // 处理配置管理
  const handleManageConfigs = useCallback((tenant: Tenant) => {
    setConfigManagingTenant(tenant)
    setConfigDialogOpen(true)
  }, [])

  // 处理批量导出
  const handleBatchExport = useCallback(() => {
    const selectedTenants = getSelectedTenants(tenants)
    if (selectedTenants.length === 0) return
    
    exportTenants(selectedTenants.map(t => t.id))
  }, [tenants, getSelectedTenants, exportTenants])

  // 处理批量删除
  const handleBatchDelete = useCallback(() => {
    const selectedTenants = getSelectedTenants(tenants)
    if (selectedTenants.length === 0) return
    
    batchDeleteTenants(selectedTenants)
  }, [tenants, getSelectedTenants, batchDeleteTenants])

  // 处理表单提交
  const handleFormSubmit = useCallback(async (data: any) => {
    if (editingTenant) {
      return await updateTenant(editingTenant.id, data)
    } else {
      return await createTenant(data)
    }
  }, [editingTenant, createTenant, updateTenant])

  // 处理导入
  const handleImport = () => {
    console.log('📥 导入租户')
    // TODO: 实现租户导入功能
  }

  // 处理刷新
  const handleRefresh = () => {
    console.log('🔄 刷新数据')
    executeSearch()
  }

  return (
    <PagePermissionWrapper module="tenant">
      <TenantErrorBoundary>
        <div className="space-y-6">
          {/* 页面标题 */}
          <div>
            <h1 className="text-3xl font-bold tracking-tight">租户管理</h1>
            <p className="text-muted-foreground">
              管理系统中的所有租户，包括租户信息、状态、配置等
            </p>
          </div>

          {/* 权限控制的工具栏 */}
          <PermissionToolbar
            module="tenant"
            searchComponent={
              <Card>
                <CardContent className="p-6">
                  <TenantSearchForm
                    formState={formState}
                    onKeywordChange={updateKeyword}
                    onAdvancedFiltersChange={updateAdvancedFilters}
                    onToggleAdvanced={toggleAdvanced}
                    onSearch={executeSearch}
                    onReset={resetSearch}
                    onQuickFilter={quickFilterByStatus}
                    onKeyPress={handleKeyPress}
                    searchStatus={getSearchStatus()}
                    searchSummary={getSearchSummary()}
                    loading={loading}
                  />
                </CardContent>
              </Card>
            }
            primaryActions={[
              {
                action: 'add',
                config: {
                  text: '新增租户',
                  icon: Plus,
                  onClick: openCreateDialog,
                  disabled: loading,
                }
              }
            ]}
            secondaryActions={[
              {
                action: 'import',
                config: {
                  text: '导入',
                  icon: Upload,
                  variant: 'outline',
                  onClick: handleImport,
                  disabled: loading,
                }
              },
              {
                action: 'export',
                config: {
                  text: '导出',
                  icon: Download,
                  variant: 'outline',
                  onClick: () => exportTenants(),
                  disabled: loading,
                }
              }
            ]}
            customActions={
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            }
          />

      {/* 批量操作栏 */}
      {selectionStats.hasSelection && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-muted-foreground">
                  已选择 {selectionStats.selectedCount} 项
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearSelection}
                >
                  取消选择
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBatchExport}
                  disabled={operationLoading}
                >
                  <Download className="w-4 h-4 mr-2" />
                  批量导出
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBatchDelete}
                  disabled={operationLoading}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  批量删除
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 租户表格 */}
      <Card>
        <CardContent className="p-0">
          <TenantTable
            data={tenants}
            loading={loading}
            selectedIds={selectedIds}
            onSelectionChange={(ids) => {
              // 使用setSelection方法直接设置选择的ID
              setSelection(ids, tenants)
            }}
            onSelectAll={() => toggleSelectAll(tenants)}
            isAllSelected={isAllSelected(tenants)}
            isIndeterminate={isIndeterminate(tenants)}
            onEdit={openEditDialog}
            onDelete={openDeleteDialog}
            onToggleStatus={toggleTenantStatus}
            onManageUsers={handleManageUsers}
            onManageConfigs={handleManageConfigs}
            onViewStats={handleViewStats}
            sortConfig={sortConfig}
            onSort={handleSort}
          />
        </CardContent>
      </Card>

      {/* 分页 */}
      {total > 0 && (
        <div className="flex justify-center">
          <TenantPagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            onChange={handlePageChange}
            onRefresh={refresh}
            loading={loading}
            showSizeChanger={true}
            showQuickJumper={true}
            showTotal={pagination.showTotal}
          />
        </div>
      )}

      {/* 租户表单对话框 */}
      <Dialog open={formDialogOpen} onOpenChange={closeFormDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingTenant ? '编辑租户' : '新增租户'}
            </DialogTitle>
          </DialogHeader>
          <TenantForm
            tenant={editingTenant}
            onSubmit={handleFormSubmit}
            onCancel={closeFormDialog}
            loading={operationLoading}
          />
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={closeDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              确定要删除租户 "{deletingTenant?.tenantName}" 吗？此操作不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={closeDeleteDialog}>
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={operationLoading}
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 统计对话框 */}
      <TenantStatsDialog
        open={statsDialogOpen}
        onOpenChange={setStatsDialogOpen}
        tenant={statsViewingTenant}
      />

      {/* 配置管理对话框 */}
      <TenantConfigDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        tenant={configManagingTenant}
        onSuccess={() => {
          // 配置更新成功后的回调
          console.log('租户配置更新成功')
        }}
      />

      {/* 用户管理对话框 */}
      <TenantUserDialog
        open={userDialogOpen}
        onOpenChange={setUserDialogOpen}
        tenant={userManagingTenant}
        onSuccess={() => {
          // 用户管理操作成功后的回调
          console.log('租户用户管理操作成功')
        }}
      />
        </div>
      </TenantErrorBoundary>
    </PagePermissionWrapper>
  )
}

export default TenantList
