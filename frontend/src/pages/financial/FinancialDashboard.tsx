import React, { useState, useCallback } from 'react'
import { RefreshCw, Download, Settings, TrendingUp, FileSpreadsheet, FileText, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Switch } from '@/components/ui'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'


import {
  CategoryTabs
} from '@/components/financial'
import { useFinancialStats } from '@/hooks/useFinancialStats'
import { usePageTitle } from '@/router/guards'
import { useToast } from '@/hooks/useToast'
import { cn } from '@/utils'
import { formatTimeRangeText, formatTimeForApi, getPresetTimeRange } from '@/utils/financial'
import { TimeRangePreset } from '@/types/financial'
import { downloadCSV, exportToExcel, validateExportData } from '@/utils/export'
import type { DateRange } from "react-day-picker"
import type {
  FinancialStatsCategory,
  FinancialStatsItem,
  FinancialStatsRequest,
  TimeRange
} from '@/types/financial'
import { FinancialStatsCategory as FSCategory } from '@/types/financial'
import DateRangePicker from "@/components/ui/date-range-picker"

/**
 * 将 DateRange 转换为 FinancialStatsRequest 参数
 * 只有当起始日期和结束日期都存在时才返回有效的请求参数
 */
const convertDateRangeToRequest = (dateRange: DateRange | undefined, includeAnchor: boolean): FinancialStatsRequest | null => {
  // 必须同时有起始日期和结束日期才能构成完整的查询请求
  if (!dateRange?.from || !dateRange?.to) {
    return null
  }

  const startTime = formatTimeForApi(dateRange.from.toISOString())
  const endTime = formatTimeForApi(dateRange.to.toISOString())

  return {
    startTime,
    endTime,
    includeAnchor
  }
}



/**
 * 检查日期范围是否完整（包含起始日期和结束日期）
 */
const isDateRangeComplete = (dateRange: DateRange | undefined): boolean => {
  return !!(dateRange?.from && dateRange?.to)
}

/**
 * 检查日期范围是否正在选择中（只有起始日期，没有结束日期）
 */
const isDateRangeSelecting = (dateRange: DateRange | undefined): boolean => {
  return !!(dateRange?.from && !dateRange?.to)
}

/**
 * 财务数据仪表板页面组件
 * 集成时间选择器、主播数据开关和数据展示组件
 */
const FinancialDashboard: React.FC = () => {
  // 设置页面标题
  usePageTitle('财务数据统计')

  const { toast } = useToast()

  // 状态管理
  const [selectedRange, setSelectedRange] = useState<DateRange | undefined>()
  const [includeAnchor, setIncludeAnchor] = useState(true)
  const [activeTab, setActiveTab] = useState<FinancialStatsCategory>('total' as FinancialStatsCategory)
  const [autoRefresh, setAutoRefresh] = useState(false)

  // 查询控制状态 - 当前生效的查询参数
  const [currentQueryParams, setCurrentQueryParams] = useState<FinancialStatsRequest>(() => ({
    ...getPresetTimeRange(TimeRangePreset.TODAY),
    includeAnchor: true
  }))

  // 处理日期范围确认选择
  const handleDateRangeConfirm = useCallback((dateRange: DateRange | undefined) => {
    if (dateRange?.from && dateRange?.to) {
      // 验证日期范围
      const validation = validateDateRange(dateRange)
      if (!validation.isValid) {
        toast({
          title: '日期范围无效',
          description: validation.error,
          variant: 'destructive'
        })
        return
      }

      // 转换并设置查询参数
      const queryParams = convertDateRangeToRequest(dateRange, includeAnchor)
      if (queryParams) {
        setCurrentQueryParams(queryParams)
        setSelectedRange(dateRange) // 同步选择状态
        toast({
          title: '查询已更新',
          description: '正在获取新的财务数据...'
        })
      }
    } else if (dateRange === undefined) {
      // 清除选择，使用默认今日范围
      const defaultParams = {
        ...getPresetTimeRange(TimeRangePreset.TODAY),
        includeAnchor
      }
      setCurrentQueryParams(defaultParams)
      setSelectedRange(undefined)
      toast({
        title: '已重置',
        description: '使用默认时间范围查询数据'
      })
    }
  }, [includeAnchor, toast])

  // 检查日期范围状态
  const isRangeComplete = isDateRangeComplete(selectedRange)
  const isRangeSelecting = isDateRangeSelecting(selectedRange)

  /**
   * 验证日期范围是否有效
   */
  const validateDateRange = (dateRange: DateRange | undefined): { isValid: boolean; error?: string } => {
    if (!dateRange?.from || !dateRange?.to) {
      return { isValid: false, error: '请选择完整的日期范围' }
    }

    if (dateRange.to < dateRange.from) {
      return { isValid: false, error: '结束日期不能早于开始日期' }
    }

    // 检查日期范围是否过大（例如：不超过1年）
    const daysDiff = Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))
    if (daysDiff > 365) {
      return { isValid: false, error: '日期范围不能超过365天' }
    }

    return { isValid: true }
  }



  // 数据获取 - 使用当前查询参数，只有在启用查询时才执行
  const {
    data,
    loading,
    refetch,
    forceRefresh,
    lastUpdated
  } = useFinancialStats(
    currentQueryParams,
    {
      refreshInterval: autoRefresh ? 30000 : 0, // 30秒自动刷新
      onSuccess: () => {
        if (autoRefresh) {
          toast({
            title: '数据已更新',
            description: '财务数据已自动刷新',
            duration: 2000
          })
        }
      },
      onError: (error) => {
        toast({
          title: '数据获取失败',
          description: error.message,
          variant: 'destructive'
        })
      }
    }
  )


  /**
   * 处理主播数据开关变化
   */
  const handleIncludeAnchorChange = useCallback((checked: boolean) => {
    setIncludeAnchor(checked)
  }, [])

  /**
   * 处理标签页切换
   */
  const handleTabChange = useCallback((category: FinancialStatsCategory) => {
    setActiveTab(category)
  }, [])

  /**
   * 处理手动刷新
   */
  const handleRefresh = useCallback(async () => {
    try {
      await refetch()
      toast({
        title: '刷新成功',
        description: '财务数据已更新',
        duration: 2000
      })
    } catch (error) {
      // 错误已在Hook中处理
    }
  }, [refetch, toast])

  /**
   * 处理强制刷新（清除缓存）
   */
  const handleForceRefresh = useCallback(async () => {
    try {
      await forceRefresh()
      toast({
        title: '强制刷新成功',
        description: '已清除缓存并重新获取数据',
        duration: 2000
      })
    } catch (error) {
      // 错误已在Hook中处理
    }
  }, [forceRefresh, toast])

  /**
   * 处理数据导出
   */
  const handleExport = useCallback((format: 'csv' | 'excel' = 'csv') => {
    // 检查数据是否存在
    if (!data) {
      toast({
        title: '导出失败',
        description: '暂无数据可导出',
        variant: 'destructive'
      })
      return
    }

    // 验证导出数据
    const validation = validateExportData(data)
    if (!validation.valid) {
      toast({
        title: '导出失败',
        description: validation.message || '数据验证失败',
        variant: 'destructive'
      })
      return
    }

    try {
      // 生成时间范围描述用于文件名
      const timeRange: TimeRange = {
        startTime: currentQueryParams.startTime,
        endTime: currentQueryParams.endTime
      }
      const timeRangeDesc = formatTimeRangeText(timeRange)

      // 执行导出
      if (format === 'excel') {
        exportToExcel(data, {
          format: 'excel',
          categories: [FSCategory.USER, FSCategory.ANCHOR, FSCategory.TOTAL, FSCategory.BUSINESS],
          filename: timeRangeDesc
        })
        toast({
          title: '导出成功',
          description: `已导出Excel格式文件，包含${validation.itemCount}条数据`,
          duration: 3000
        })
      } else {
        downloadCSV(data, {
          format: 'csv',
          categories: [FSCategory.USER, FSCategory.ANCHOR, FSCategory.TOTAL, FSCategory.BUSINESS],
          filename: timeRangeDesc
        })
        toast({
          title: '导出成功',
          description: `已导出CSV格式文件，包含${validation.itemCount}条数据`,
          duration: 3000
        })
      }
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: '导出失败',
        description: '导出过程中发生错误，请重试',
        variant: 'destructive'
      })
    }
  }, [data, currentQueryParams, toast])

  /**
   * 处理卡片点击
   */
  const handleCardClick = useCallback((
    item: FinancialStatsItem, 
    category: FinancialStatsCategory
  ) => {
    // TODO: 实现卡片点击详情功能
  }, [])

  /**
   * 获取数据统计摘要
   */
  const getDataSummary = () => {
    if (!data) return null
    const totalItems = [
      ...data.userStats,
      ...data.anchorStats,
      ...data.totalStats,
      ...data.businessStats
    ].length

    return {
      totalItems,
      categories: {
        user: data.userStats.length,
        anchor: data.anchorStats.length,
        total: data.totalStats.length,
        business: data.businessStats.length
      }
    }
  }

  const summary = getDataSummary()

  return (
    <div className="space-y-6">
      {/* 页面标题和操作区域 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center">
            <TrendingUp className="mr-3 h-8 w-8 text-primary" />
            财务数据统计
          </h1>
          <p className="text-muted-foreground mt-1">
            查看和分析平台财务数据，支持多维度统计和时间范围筛选
          </p>
        </div>
        
        {/* 操作按钮组 */}
        <div className="flex items-center space-x-3">
          {/* 自动刷新开关 */}
          <div className="flex items-center space-x-2">
            <Switch
              id="auto-refresh"
              checked={autoRefresh}
              onCheckedChange={setAutoRefresh}
              disabled={loading}
            />
            <Label htmlFor="auto-refresh" className="text-sm">
              自动刷新
            </Label>
          </div>

          {/* 刷新按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center space-x-2"
          >
            <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
            <span>刷新</span>
          </Button>

          {/* 强制刷新按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleForceRefresh}
            disabled={loading}
            className="flex items-center space-x-2"
          >
            <RefreshCw className="h-4 w-4" />
            <span>强制刷新</span>
          </Button>

          {/* 导出按钮 */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                disabled={loading || !data}
                className="flex items-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>导出</span>
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48 p-2" align="end">
              <div className="space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleExport('csv')}
                  disabled={loading || !data}
                  className="w-full justify-start"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  导出为CSV
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleExport('excel')}
                  disabled={loading || !data}
                  className="w-full justify-start"
                >
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  导出为Excel
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          {/* 设置按钮 */}
          <Button
            variant="outline"
            size="sm"
            className="flex items-center space-x-2"
          >
            <Settings className="h-4 w-4" />
            <span>设置</span>
          </Button>
        </div>
      </div>

      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">查询条件</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 时间范围选择器 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">时间范围</Label>
              <DateRangePicker onDateChange={handleDateRangeConfirm} className="max-w-md" />

              {/* 日期选择状态提示 */}
              {isRangeSelecting && (
                <div className="text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-md border border-amber-200">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                    <span>请选择结束日期，然后点击"确认选择"按钮</span>
                  </div>
                </div>
              )}
              {!isRangeComplete && !isRangeSelecting && (
                <div className="text-sm text-gray-500">
                  当前使用默认时间范围：今日
                </div>
              )}
            </div>

            {/* 主播数据开关 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">数据选项</Label>
              <div className="flex items-center space-x-4 p-3 border border-border rounded-md">
                <Switch
                  id="include-anchor"
                  checked={includeAnchor}
                  onCheckedChange={handleIncludeAnchorChange}
                  disabled={loading}
                />
                <div className="flex-1">
                  <Label htmlFor="include-anchor" className="font-medium">
                    包含主播数据
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    是否在统计中包含主播相关的收入和支出数据
                  </p>
                </div>
              </div>
            </div>
          </div>



          {/* 数据摘要 */}
          {summary && (
            <div className="pt-4 border-t border-border">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-primary">
                    {summary.totalItems}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    总统计项
                  </div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {summary.categories.user}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    用户相关
                  </div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {summary.categories.anchor}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    主播相关
                  </div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {summary.categories.total}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    合计统计
                  </div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {summary.categories.business}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    其他业务
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 最后更新时间 */}
          {lastUpdated && (
            <div className="text-sm text-muted-foreground text-right">
              最后更新: {new Date(lastUpdated).toLocaleString('zh-CN')}
            </div>
          )}
        </CardContent>
      </Card>
      {/* 数据展示区域 */}
      <CategoryTabs
        data={data}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        loading={loading}
        gridColumns={4}
        onCardClick={handleCardClick}
        className="min-h-[400px]"
      />

      {/* 加载状态遮罩 */}
      {loading && (
        <div className="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
          <Card className="p-6">
            <div className="flex items-center space-x-3">
              <RefreshCw className="h-5 w-5 animate-spin text-primary" />
              <div className="text-sm font-medium">正在加载财务数据...</div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}

export default FinancialDashboard