/**
 * 权限常量定义
 * 
 * 统一管理系统中所有的权限标识，确保前后端权限标识一致性
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 系统权限常量
 */
export const PERMISSIONS = {
  /**
   * 仪表板权限
   */
  DASHBOARD: {
    VIEW: 'dashboard:view',
  },

  /**
   * 系统管理权限
   */
  SYSTEM: {
    VIEW: 'system:view',
    
    /**
     * 用户管理权限
     */
    USER: {
      LIST: 'system:user:list',
      VIEW: 'system:user:view',
      ADD: 'system:user:add',
      EDIT: 'system:user:edit',
      DELETE: 'system:user:delete',
      IMPORT: 'system:user:import',
      EXPORT: 'system:user:export',
      RESET_PASSWORD: 'system:user:reset-password',
    },

    /**
     * 角色管理权限
     */
    ROLE: {
      LIST: 'system:role:list',
      VIEW: 'system:role:view',
      ADD: 'system:role:add',
      EDIT: 'system:role:edit',
      DELETE: 'system:role:delete',
      ASSIGN_PERMISSIONS: 'system:role:assign-permissions',
      ASSIGN_MENUS: 'system:role:assign-menus',
    },

    /**
     * 权限管理权限
     */
    PERMISSION: {
      LIST: 'system:permission:list',
      VIEW: 'system:permission:view',
      ADD: 'system:permission:add',
      EDIT: 'system:permission:edit',
      DELETE: 'system:permission:delete',
    },

    /**
     * 部门管理权限
     */
    DEPT: {
      LIST: 'system:dept:list',
      VIEW: 'system:dept:view',
      ADD: 'system:dept:add',
      EDIT: 'system:dept:edit',
      DELETE: 'system:dept:delete',
    },

    /**
     * 租户管理权限
     */
    TENANT: {
      LIST: 'system:tenant:list',
      VIEW: 'system:tenant:view',
      ADD: 'system:tenant:add',
      EDIT: 'system:tenant:edit',
      DELETE: 'system:tenant:delete',
      ENABLE: 'system:tenant:enable',
      DISABLE: 'system:tenant:disable',
      STATS: 'system:tenant:stats',
      CONFIG: 'system:tenant:config',
      EXPORT: 'system:tenant:export',
      MANAGE_USERS: 'system:tenant:manage-users',
      MANAGE_CONFIG: 'system:tenant:manage-config',
      TOGGLE_STATUS: 'system:tenant:toggle-status',
    },
  },

  /**
   * 权限管理模块权限（与后端菜单结构保持一致）
   */
  AUTH: {
    VIEW: 'auth:view',
    
    /**
     * 角色管理权限
     */
    ROLE: {
      LIST: 'auth:role:list',
      VIEW: 'auth:role:view',
      ADD: 'auth:role:add',
      EDIT: 'auth:role:edit',
      DELETE: 'auth:role:delete',
      ASSIGN_PERMISSIONS: 'auth:role:assign-permissions',
    },
    MENU: {
      LIST: 'system:menu:list',
      VIEW: 'system:menu:query',
      ADD: 'system:menu:add',
      EDIT: 'system:menu:edit',
      DELETE: 'system:menu:remove',
      IMPORT: 'system:menu:import',
      EXPORT: 'system:menu:export',
      MOVE: 'system:menu:edit',
    },

    /**
     * 权限管理权限
     */
    PERMISSION: {
      LIST: 'auth:permission:list',
      VIEW: 'auth:permission:view',
      ADD: 'auth:permission:add',
      EDIT: 'auth:permission:edit',
      DELETE: 'auth:permission:delete',
    },
  },

  /**
   * 财务数据管理权限
   */
  FINANCIAL: {
    VIEW: 'financial:view',
    
    /**
     * 财务统计权限
     */
    STATS: {
      QUERY: 'financial:stats:query',
      VIEW: 'financial:stats:view',
      EXPORT: 'financial:stats:export',
    },
  },

  /**
   * 监控管理权限
   */
  MONITOR: {
    VIEW: 'monitor:view',

    /**
     * 操作日志权限
     */
    OPERLOG: {
      LIST: 'monitor:operlog:list',
      VIEW: 'monitor:operlog:view',
      DELETE: 'monitor:operlog:remove',
      EXPORT: 'monitor:operlog:export',
      CLEAR: 'monitor:operlog:clear',
    },

    /**
     * 登录日志权限
     */
    LOGINLOG: {
      LIST: 'monitor:loginlog:list',
      VIEW: 'monitor:loginlog:view',
      DELETE: 'monitor:loginlog:remove',
      EXPORT: 'monitor:loginlog:export',
      CLEAR: 'monitor:loginlog:clear',
    },

    /**
     * 系统信息权限
     */
    SERVER: {
      LIST: 'monitor:server:list',
      VIEW: 'monitor:server:view',
    },

    /**
     * 在线用户权限
     */
    ONLINE: {
      LIST: 'monitor:online:list',
      FORCE_LOGOUT: 'monitor:online:force-logout',
    },
  },

  /**
   * 运营管理权限
   */
  OPERATIONS: {
    VIEW: 'operations:view',

    /**
     * 主播管理权限
     */
    ANCHORS: {
      LIST: 'operations:anchors:list',
      VIEW: 'operations:anchors:view',
      STATS: 'operations:anchors:stats',
      FIRST_RECHARGE: 'operations:anchors:first-recharge',
      SUB_USERS: 'operations:anchors:sub-users',
    },

    /**
     * 用户管理权限
     */
    USERS: {
      LIST: 'operations:users:list',
      VIEW: 'operations:users:view',
      CONSUME: 'operations:users:consume',
      RECHARGE: 'operations:users:recharge',
    },

    /**
     * 统计数据权限
     */
    STATS: {
      VIEW_PROFIT: 'operations:stats:view-profit',           // 查看实际利润
      VIEW_PENDING_AMOUNT: 'operations:stats:view-pending',  // 查看待发货金额
      VIEW_SHIPPED_AMOUNT: 'operations:stats:view-shipped',  // 查看实际发货金额
      VIEW_BACKPACK_VALUE: 'operations:stats:view-backpack', // 查看背包总价值
      VIEW_FINANCIAL: 'operations:stats:view-financial',     // 查看财务相关数据（包含以上所有）
    },
  },
} as const

/**
 * 角色常量定义
 */
export const ROLES = {
  /**
   * 超级管理员角色
   */
  SUPER_ADMIN: 'SUPER_ADMIN',
  
  /**
   * 系统管理员角色
   */
  ADMIN: 'ADMIN',
  
  /**
   * 普通用户角色
   */
  USER: 'USER',
  
  /**
   * 租户管理员角色
   */
  TENANT_ADMIN: 'TENANT_ADMIN',
} as const

/**
 * 特殊权限常量
 */
export const SPECIAL_PERMISSIONS = {
  /**
   * 超级管理员通配符权限
   */
  SUPER_ADMIN_WILDCARD: '*:*:*',
  
  /**
   * 所有权限通配符
   */
  ALL_PERMISSIONS: '*',
} as const

/**
 * 权限类型定义
 */
export type PermissionKey =
  | typeof PERMISSIONS.DASHBOARD[keyof typeof PERMISSIONS.DASHBOARD]
  | typeof PERMISSIONS.SYSTEM.USER[keyof typeof PERMISSIONS.SYSTEM.USER]
  | typeof PERMISSIONS.SYSTEM.ROLE[keyof typeof PERMISSIONS.SYSTEM.ROLE]
  | typeof PERMISSIONS.SYSTEM.PERMISSION[keyof typeof PERMISSIONS.SYSTEM.PERMISSION]
  | typeof PERMISSIONS.SYSTEM.DEPT[keyof typeof PERMISSIONS.SYSTEM.DEPT]
  | typeof PERMISSIONS.SYSTEM.TENANT[keyof typeof PERMISSIONS.SYSTEM.TENANT]
  | typeof PERMISSIONS.AUTH.ROLE[keyof typeof PERMISSIONS.AUTH.ROLE]
  | typeof PERMISSIONS.AUTH.MENU[keyof typeof PERMISSIONS.AUTH.MENU]
  | typeof PERMISSIONS.AUTH.PERMISSION[keyof typeof PERMISSIONS.AUTH.PERMISSION]
  | typeof PERMISSIONS.FINANCIAL.STATS[keyof typeof PERMISSIONS.FINANCIAL.STATS]
  | typeof PERMISSIONS.MONITOR.OPERLOG[keyof typeof PERMISSIONS.MONITOR.OPERLOG]
  | typeof PERMISSIONS.MONITOR.LOGINLOG[keyof typeof PERMISSIONS.MONITOR.LOGINLOG]
  | typeof PERMISSIONS.MONITOR.SERVER[keyof typeof PERMISSIONS.MONITOR.SERVER]
  | typeof PERMISSIONS.MONITOR.ONLINE[keyof typeof PERMISSIONS.MONITOR.ONLINE]
  | typeof PERMISSIONS.OPERATIONS.ANCHORS[keyof typeof PERMISSIONS.OPERATIONS.ANCHORS]
  | typeof PERMISSIONS.OPERATIONS.USERS[keyof typeof PERMISSIONS.OPERATIONS.USERS]
  | typeof PERMISSIONS.OPERATIONS.STATS[keyof typeof PERMISSIONS.OPERATIONS.STATS]
  | typeof SPECIAL_PERMISSIONS[keyof typeof SPECIAL_PERMISSIONS]

/**
 * 角色类型定义
 */
export type RoleKey = typeof ROLES[keyof typeof ROLES]

/**
 * 权限工具函数
 */
export const PermissionUtils = {
  /**
   * 检查权限是否为通配符权限
   */
  isWildcardPermission: (permission: string): boolean => {
    return permission.includes('*')
  },

  /**
   * 获取权限的模块前缀
   */
  getPermissionModule: (permission: string): string => {
    return permission.split(':')[0] || ''
  },

  /**
   * 获取权限的操作类型
   */
  getPermissionAction: (permission: string): string => {
    const parts = permission.split(':')
    return parts[parts.length - 1] || ''
  },

  /**
   * 构建权限标识
   */
  buildPermission: (module: string, resource: string, action: string): string => {
    return `${module}:${resource}:${action}`
  },

  /**
   * 验证权限格式
   */
  validatePermissionFormat: (permission: string): boolean => {
    // 权限格式: module:resource:action 或 通配符
    const wildcardPattern = /^(\*|\w+):(\*|\w+):(\*|\w+)$/
    const simplePattern = /^\*$/
    return wildcardPattern.test(permission) || simplePattern.test(permission)
  },

  /**
   * 获取所有权限列表（扁平化）
   */
  getAllPermissions: (): string[] => {
    const permissions: string[] = []
    
    const extractPermissions = (obj: any): void => {
      for (const key in obj) {
        if (typeof obj[key] === 'string') {
          permissions.push(obj[key])
        } else if (typeof obj[key] === 'object') {
          extractPermissions(obj[key])
        }
      }
    }
    
    extractPermissions(PERMISSIONS)
    extractPermissions(SPECIAL_PERMISSIONS)
    
    return permissions
  },

  /**
   * 根据模块获取权限列表
   */
  getPermissionsByModule: (module: keyof typeof PERMISSIONS): string[] => {
    const permissions: string[] = []
    const modulePerms = PERMISSIONS[module]
    
    const extractPermissions = (obj: any): void => {
      for (const key in obj) {
        if (typeof obj[key] === 'string') {
          permissions.push(obj[key])
        } else if (typeof obj[key] === 'object') {
          extractPermissions(obj[key])
        }
      }
    }
    
    extractPermissions(modulePerms)
    return permissions
  },
}
