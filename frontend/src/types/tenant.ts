/**
 * 租户管理相关类型定义
 */

import type { PageRequest } from './api'

/**
 * 租户状态枚举
 */
export enum TenantStatus {
  DISABLED = 0, // 禁用
  ENABLED = 1,  // 启用
}

/**
 * 租户状态选项
 */
export const TENANT_STATUS_OPTIONS = [
  { label: '启用', value: TenantStatus.ENABLED },
  { label: '禁用', value: TenantStatus.DISABLED },
]

/**
 * 获取租户状态标签
 */
export const getTenantStatusLabel = (status: number): string => {
  const option = TENANT_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || '未知'
}

/**
 * 租户信息
 */
export interface Tenant {
  /** 租户ID */
  id: number
  /** 租户编码 */
  tenantCode: string
  /** 租户名称 */
  tenantName: string
  /** 联系人 */
  contactName?: string
  /** 联系电话 */
  contactPhone?: string
  /** 联系邮箱 */
  contactEmail?: string
  /** 租户状态 */
  status: number
  /** 过期时间 */
  expireTime?: string
  /** 最大用户数量 */
  maxUserCount?: number
  /** 备注 */
  remark?: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 创建人 */
  createBy: number
  /** 更新人 */
  updateBy: number
  /** 版本号 */
  version: number
}

/**
 * 租户查询请求
 */
export interface TenantQueryRequest extends PageRequest {
  /** 搜索关键词（同时搜索租户编码和租户名称） */
  keyword?: string
  /** 租户编码 */
  tenantCode?: string
  /** 租户名称 */
  tenantName?: string
  /** 联系人 */
  contactName?: string
  /** 联系电话 */
  contactPhone?: string
  /** 联系邮箱 */
  contactEmail?: string
  /** 租户状态 */
  status?: number
  /** 创建时间开始 */
  startTime?: string
  /** 创建时间结束 */
  endTime?: string
}

/**
 * 租户创建请求
 */
export interface TenantCreateRequest {
  /** 租户编码 */
  tenantCode: string
  /** 租户名称 */
  tenantName: string
  /** 联系人 */
  contactName?: string
  /** 联系电话 */
  contactPhone?: string
  /** 联系邮箱 */
  contactEmail?: string
  /** 租户状态 */
  status: number
  /** 过期时间 */
  expireTime?: string
  /** 最大用户数量 */
  maxUserCount?: number
  /** 备注 */
  remark?: string
}

/**
 * 租户更新请求
 */
export interface TenantUpdateRequest {
  /** 租户ID */
  id: number
  /** 租户名称 */
  tenantName: string
  /** 联系人 */
  contactName?: string
  /** 联系电话 */
  contactPhone?: string
  /** 联系邮箱 */
  contactEmail?: string
  /** 租户状态 */
  status: number
  /** 过期时间 */
  expireTime?: string
  /** 最大用户数量 */
  maxUserCount?: number
  /** 备注 */
  remark?: string
}

/**
 * 租户统计信息
 */
export interface TenantStats {
  /** 租户ID */
  tenantId: number
  /** 用户总数 */
  userCount: number
  /** 角色总数 */
  roleCount: number
  /** 部门总数 */
  deptCount: number
  /** 存储使用量(MB) */
  storageUsed: number
  /** 最后登录时间 */
  lastLoginTime?: string
}

/**
 * 租户配置信息
 */
export interface TenantConfig {
  /** 租户ID */
  tenantId: number
  /** 配置键 */
  configKey: string
  /** 配置值 */
  configValue: string
  /** 配置描述 */
  configDesc?: string
  /** 是否系统配置 */
  isSystem: boolean
}

/**
 * 租户用户分配请求
 */
export interface TenantUserAssignRequest {
  /** 租户ID */
  tenantId: number
  /** 用户ID列表 */
  userIds: number[]
}

/**
 * 租户数据导出请求
 */
export interface TenantExportRequest {
  /** 租户ID列表 */
  tenantIds?: number[]
  /** 导出格式 */
  format: 'excel' | 'csv'
  /** 包含统计信息 */
  includeStats?: boolean
}
