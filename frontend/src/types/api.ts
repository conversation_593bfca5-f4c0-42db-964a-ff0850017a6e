/**
 * API响应基础结构
 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp: string
}

/**
 * 分页请求参数
 * 对应后端PageQuery类
 */
export interface PageRequest {
  pageNum: number  // 对应后端pageNum
  pageSize: number // 对应后端pageSize
  orderBy?: string // 对应后端orderBy
  orderDirection?: 'ASC' | 'DESC' // 对应后端orderDirection
}

/**
 * 分页响应结构
 */
export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 用户信息
 */
export interface User {
  id: number
  username: string
  nickname?: string
  realName?: string
  email?: string
  phone?: string
  avatar?: string
  gender?: number
  birthday?: string
  status: number
  isAdmin: number
  remark?: string
  tenantId: number
  createTime: string
  updateTime: string
  createBy?: number
  updateBy?: number
  deleted: number
}

/**
 * 角色信息 - 已迁移到 role.ts
 * @deprecated 请使用 role.ts 中的 Role 接口
 */

/**
 * 权限信息
 */
export interface Permission {
  id: number
  permissionName: string
  permissionCode: string  // 修复：使用permissionCode而不是permissionKey
  permissionType: string  // 修复：后端返回的是字符串类型
  parentId?: number
  resourcePath?: string   // 修复：使用resourcePath而不是path
  method?: string         // 修复：添加HTTP方法字段
  sortOrder?: number      // 修复：使用sortOrder而不是orderNum
  status: number
  remark?: string
  tenantId: number
  createTime: string
  updateTime: string
  createBy?: number
  updateBy?: number
  deleted: number
  children?: Permission[]
}

/**
 * 菜单信息
 */
export interface Menu {
  id: number
  parentId?: number
  name: string
  title: string
  path?: string
  component?: string
  icon?: string
  permission?: string
  permissionCode?: string  // 权限编码字段
  type: number            // 0-目录，1-菜单，2-按钮(权限点)
  sort: number
  visible: boolean
  enabled: boolean
  children?: Menu[]

  // 权限相关扩展字段
  isPermission?: boolean  // 是否为权限点（type === 2）
  permissionName?: string // 权限名称（等同于菜单名称）
  permissionDescription?: string // 权限描述
}

/**
 * 登录请求参数
 */
export interface LoginRequest {
  username: string
  password: string
  captcha?: string
  captchaKey?: string
  rememberMe?: boolean
}

/**
 * 验证码响应数据
 */
export interface CaptchaResponse {
  captchaId: string
  captchaImage: string
  expireTime: number
  captchaType: string
}

/**
 * 验证码验证请求
 */
export interface CaptchaVerifyRequest {
  captchaId: string
  captchaCode: string
}

/**
 * 验证码验证响应
 */
export interface CaptchaVerifyResponse {
  valid: boolean
  message: string
}

/**
 * 验证码配置响应
 */
export interface CaptchaConfigResponse {
  enabled: boolean
  type: string
  length: number
  expireTime: number
  caseSensitive: boolean
  image: {
    width: number
    height: number
  }
}

/**
 * 登录响应数据 - 匹配后端LoginResponse结构
 */
export interface LoginResponse {
  accessToken: string
  tokenType: string
  expiresIn: number
  userInfo: UserInfo
  permissions: string[]
  roles: string[]
}

/**
 * 用户信息 - 登录响应中的用户信息
 */
export interface UserInfo {
  userId: number
  username: string
  nickname?: string
  realName?: string
  email?: string
  phone?: string
  avatar?: string
  tenantId: number
  tenantName?: string
}
