import { create } from 'zustand'
import { CaptchaService } from '@/services'
import type { CaptchaResponse } from '@/types'

/**
 * 验证码状态接口
 */
interface CaptchaState {
  // 状态
  captcha: CaptchaResponse | null
  loading: boolean
  error: string | null
  retryCount: number
  maxRetries: number
  isNetworkError: boolean

  // 操作方法
  generateCaptcha: () => Promise<void>
  verifyCaptcha: (captchaId: string, captchaCode: string) => Promise<boolean>
  invalidateCaptcha: (captchaId: string) => Promise<void>
  clearCaptcha: () => void
  clearError: () => void
  resetRetryCount: () => void
}
/**
 * 创建验证码状态管理
 */
export const useCaptchaStore = create<CaptchaState>((set, get) => ({
  // 初始状态
  captcha: null,
  loading: false,
  error: null,
  retryCount: 0,
  maxRetries: 3,
  isNetworkError: false,

  /**
   * 生成验证码
   */
  generateCaptcha: async () => {
    const state = get()

    // 检查是否已达到最大重试次数
    if (state.retryCount >= state.maxRetries) {
      const errorMessage = state.isNetworkError
        ? '网络连接失败，请检查后端服务是否启动'
        : '验证码生成失败次数过多，请稍后重试'
      set({
        loading: false,
        error: errorMessage
      })
      throw new Error(errorMessage)
    }

    try {
      set({ loading: true, error: null, isNetworkError: false })
      const data: any = await CaptchaService.generate()
      set({
        captcha: data,
        loading: false,
        error: null,
        retryCount: 0, // 成功后重置重试计数
        isNetworkError: false
      })
    } catch (error) {
      const isNetworkError = error instanceof Error && (
        error.message.includes('ECONNREFUSED') ||
        error.message.includes('Network Error') ||
        error.message.includes('fetch')
      )

      const errorMessage = isNetworkError
        ? '无法连接到服务器，请检查网络连接或后端服务状态'
        : (error instanceof Error ? error.message : '生成验证码失败')

      set({
        loading: false,
        error: errorMessage,
        captcha: null,
        retryCount: state.retryCount + 1,
        isNetworkError
      })
      throw error
    }
  },

  /**
   * 验证验证码
   */
  verifyCaptcha: async (captchaId: string, captchaCode: string) => {
    try {
      set({ loading: true, error: null })

      const response = await CaptchaService.verify({
        captchaId,
        captchaCode
      })
      const isValid = response.data.valid
      set({ loading: false, error: null })

      if (isValid) {
        // 验证成功后清除验证码
        set({ captcha: null })
      }

      return isValid
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '验证码验证失败'
      set({
        loading: false,
        error: errorMessage
      })
      return false
    }
  },

  /**
   * 使验证码失效
   */
  invalidateCaptcha: async (captchaId: string) => {
    try {
      await CaptchaService.invalidate(captchaId)
    } catch (error) {
      // 不抛出错误，因为这是一个可选操作
    }
  },

  /**
   * 清除验证码
   */
  clearCaptcha: () => {
    set({ 
      captcha: null, 
      error: null 
    })
  },

  /**
   * 清除错误信息
   */
  clearError: () => {
    set({ error: null })
  },

  /**
   * 重置重试计数
   */
  resetRetryCount: () => {
    set({ retryCount: 0, error: null, isNetworkError: false })
  },

}))

/**
 * 验证码Hook - 提供便捷的验证码操作方法
 */
export const useCaptcha = () => {
  const store = useCaptchaStore()

  /**
   * 刷新验证码
   */
  const refreshCaptcha = async () => {
    try {
      // 如果有当前验证码，先使其失效
      if (store.captcha?.captchaId) {
        await store.invalidateCaptcha(store.captcha.captchaId)
      }

      // 生成新验证码
      await store.generateCaptcha()
    } catch (error) {
      throw error
    }
  }

  return {
    ...store,
    refreshCaptcha,
  }
}
