package com.jcloud.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * CORS跨域配置 - 基于Context7 Spring Boot最佳实践
 * 支持前端应用(localhost:5173)的跨域请求
 * 与sa-token认证系统完全兼容，不依赖Spring Security
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    /**
     * 全局CORS配置 - 基于Context7最佳实践
     * 配置允许的域名、方法、请求头等
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                // 允许的前端域名
                .allowedOrigins(
                        // Vite开发服务器
                        "http://localhost:5173",
                        "http://localhost:5174",
                        // React开发服务器备用端口
                        "http://localhost:3000",
                        // 本地IP访问
                        "http://127.0.0.1:5173",
                        "http://127.0.0.1:5174",
                        // 本地IP访问备用端口
                        "http://127.0.0.1:3000"
                )
                // 允许的HTTP方法
                .allowedMethods(
                        "GET", "POST", "PUT", "DELETE",
                        "OPTIONS", "HEAD", "PATCH"
                )
                // 允许的请求头
                .allowedHeaders(
                        // sa-token认证头
                        "Authorization",
                        // 内容类型
                        "Content-Type",
                        // 接受类型
                        "Accept",
                        // 来源
                        "Origin",
                        // 预检请求方法
                        "Access-Control-Request-Method",
                        // 预检请求头
                        "Access-Control-Request-Headers",
                        // AJAX请求标识
                        "X-Requested-With",
                        // 多租户ID
                        "Tenant-Id",
                        // 代理转发IP
                        "X-Forwarded-For",
                        // 代理转发协议
                        "X-Forwarded-Proto",
                        // 缓存控制
                        "Cache-Control",
                        // 缓存指令
                        "Pragma"
                )
                // 允许暴露的响应头
                .exposedHeaders(
                        "Authorization",    // 返回新的token
                        "Content-Type",     // 响应内容类型
                        "Content-Length",   // 响应内容长度
                        "X-Total-Count",    // 分页总数
                        "X-Current-Page",   // 当前页码
                        "X-Page-Size"      // 页面大小
                )
                // 允许携带凭证(cookies, authorization headers)
                .allowCredentials(true)
                // 预检请求缓存时间(秒)
                // 1小时
                .maxAge(3600);
    }


}
