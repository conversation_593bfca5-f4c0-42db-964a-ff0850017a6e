package com.jcloud.admin.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.FinancialStatsService;
import com.jcloud.common.dto.FinancialStatsRequest;
import com.jcloud.common.dto.FinancialStatsResponse;
import com.jcloud.common.dto.GroupedFinancialStatsResponse;
import com.jcloud.common.exception.FinancialDataException;
import com.jcloud.common.mapper.FinancialStatsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 财务统计服务实现类
 * 提供财务数据查询和处理的具体实现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinancialStatsServiceImpl implements FinancialStatsService {
    
    private final FinancialStatsMapper financialStatsMapper;
    
    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 统计分类常量
     */
    private static final String USER_STATS_CATEGORY = "用户相关统计";
    private static final String ANCHOR_STATS_CATEGORY = "主播相关统计";
    private static final String TOTAL_STATS_CATEGORY = "合计统计";
    private static final String BUSINESS_STATS_CATEGORY = "其他业务统计";
    
    @Override
    public GroupedFinancialStatsResponse getFinancialStats(FinancialStatsRequest request) {
        log.info("开始获取财务统计数据，请求参数：{}", request);
        
        try {
            // 参数验证
            validateTimeParams(request.getStartTime(), request.getEndTime());
            
            // 格式化时间参数
            String formattedStartTime = formatTimeParam(request.getStartTime());
            String formattedEndTime = formatTimeParam(request.getEndTime());
            
            log.debug("格式化后的时间参数 - 开始时间：{}，结束时间：{}", formattedStartTime, formattedEndTime);
            
            // 调用存储过程获取原始数据
            List<FinancialStatsResponse> rawData = financialStatsMapper.getStatisticsReport(
                formattedStartTime,
                formattedEndTime,
                request.getIncludeAnchor()
            );
            
            log.info("存储过程返回数据条数：{}", rawData != null ? rawData.size() : 0);
            
            // 数据分组处理
            GroupedFinancialStatsResponse response = groupStatsByCategory(rawData);
            
            log.info("财务统计数据获取成功");
            return response;
            
        } catch (DataAccessException e) {
            log.error("数据库访问异常，获取财务统计数据失败", e);
            throw FinancialDataException.procedureCallError("存储过程调用失败：" + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取财务统计数据时发生未知异常", e);
            throw new FinancialDataException("获取财务统计数据失败：" + e.getMessage(), e);
        }
    }
    
    @Override
    public GroupedFinancialStatsResponse getTodayFinancialStats(Boolean includeAnchor) {
        log.info("开始获取今日财务统计数据，是否包含主播数据：{}", includeAnchor);
        
        FinancialStatsRequest request = buildTodayRequest(includeAnchor);
        return getFinancialStats(request);
    }
    
    @Override
    public void validateTimeParams(String startTime, String endTime) {
        // 检查时间参数是否为空
        if (StrUtil.isBlank(startTime)) {
            throw FinancialDataException.parameterValidationError("开始时间不能为空");
        }
        if (StrUtil.isBlank(endTime)) {
            throw FinancialDataException.parameterValidationError("结束时间不能为空");
        }
        
        try {
            // 解析时间字符串
            LocalDateTime start = LocalDateTime.parse(startTime, DATE_TIME_FORMATTER);
            LocalDateTime end = LocalDateTime.parse(endTime, DATE_TIME_FORMATTER);
            
            // 验证时间范围的合理性
            if (start.isAfter(end)) {
                throw FinancialDataException.parameterValidationError("开始时间不能晚于结束时间");
            }
            
            // 验证时间范围不能超过1年
            if (start.plusYears(1).isBefore(end)) {
                throw FinancialDataException.parameterValidationError("查询时间范围不能超过1年");
            }
        } catch (DateTimeParseException e) {
            log.error("时间参数格式错误：startTime={}, endTime={}", startTime, endTime, e);
            throw FinancialDataException.parameterValidationError("时间格式不正确，请使用 yyyy-MM-dd HH:mm:ss 格式");
        }
    }
    
    @Override
    public String formatTimeParam(String timeParam) {
        if (StrUtil.isBlank(timeParam)) {
            throw FinancialDataException.parameterValidationError("时间参数不能为空");
        }
        
        try {
            // 解析并重新格式化时间，确保格式统一
            LocalDateTime dateTime = LocalDateTime.parse(timeParam, DATE_TIME_FORMATTER);
            return dateTime.format(DATE_TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("时间参数格式化失败：{}", timeParam, e);
            throw FinancialDataException.parameterValidationError("时间格式不正确，请使用 yyyy-MM-dd HH:mm:ss 格式");
        }
    }
    
    @Override
    public FinancialStatsRequest buildTodayRequest(Boolean includeAnchor) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfDay = now.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = now.toLocalDate().atTime(23, 59, 59);
        
        FinancialStatsRequest request = new FinancialStatsRequest();
        request.setStartTime(startOfDay.format(DATE_TIME_FORMATTER));
        request.setEndTime(endOfDay.format(DATE_TIME_FORMATTER));
        request.setIncludeAnchor(includeAnchor != null ? includeAnchor : true);
        
        log.debug("构建今日时间范围请求：{}", request);
        return request;
    }
    
    /**
     * 将原始统计数据按分类进行分组
     * 
     * @param rawData 原始统计数据列表
     * @return 分组后的财务统计数据
     */
    private GroupedFinancialStatsResponse groupStatsByCategory(List<FinancialStatsResponse> rawData) {
        GroupedFinancialStatsResponse response = new GroupedFinancialStatsResponse();
        
        if (rawData == null || rawData.isEmpty()) {
            log.warn("原始统计数据为空，返回空的分组数据");
            response.setUserStats(new ArrayList<>());
            response.setAnchorStats(new ArrayList<>());
            response.setTotalStats(new ArrayList<>());
            response.setBusinessStats(new ArrayList<>());
            return response;
        }
        
        // 按分类分组数据
        response.setUserStats(filterStatsByCategory(rawData, USER_STATS_CATEGORY));
        response.setAnchorStats(filterStatsByCategory(rawData, ANCHOR_STATS_CATEGORY));
        response.setTotalStats(filterStatsByCategory(rawData, TOTAL_STATS_CATEGORY));
        response.setBusinessStats(filterStatsByCategory(rawData, BUSINESS_STATS_CATEGORY));
        
        log.debug("数据分组完成 - 用户相关：{}条，主播相关：{}条，合计统计：{}条，其他业务：{}条",
            response.getUserStats().size(),
            response.getAnchorStats().size(),
            response.getTotalStats().size(),
            response.getBusinessStats().size());
        
        return response;
    }
    
    /**
     * 根据分类过滤统计数据
     * 
     * @param rawData 原始数据列表
     * @param category 分类名称
     * @return 过滤后的数据列表
     */
    private List<FinancialStatsResponse> filterStatsByCategory(List<FinancialStatsResponse> rawData, String category) {
        return rawData.stream()
            .filter(item -> category.equals(item.getCategory()))
            .collect(Collectors.toList());
    }
}