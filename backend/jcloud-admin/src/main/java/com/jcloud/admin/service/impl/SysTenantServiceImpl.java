package com.jcloud.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.mapper.SysTenantConfigMapper;
import com.jcloud.admin.mapper.SysTenantUserMapper;
import com.jcloud.admin.service.SysTenantService;
import com.jcloud.common.dto.TenantQueryRequest;
import com.jcloud.common.dto.TenantStatsDTO;
import com.jcloud.common.dto.TenantConfigDTO;
import com.jcloud.common.dto.TenantUserDTO;
import com.jcloud.common.entity.SysTenant;
import com.jcloud.common.entity.SysTenantConfig;
import com.jcloud.common.entity.SysTenantUser;
import com.jcloud.common.mapper.SysTenantMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户管理服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysTenantServiceImpl implements SysTenantService {

    private final SysTenantMapper tenantMapper;
    private final SysTenantConfigMapper tenantConfigMapper;
    private final SysTenantUserMapper tenantUserMapper;

    @Override
    public Page<SysTenant> pageTenants(TenantQueryRequest queryRequest) {
        log.debug("分页查询租户列表: {}", queryRequest);
        
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(SysTenant::getDeleted).eq(0);

        // 构建查询条件
        // 优先使用keyword进行OR查询
        if (StrUtil.isNotBlank(queryRequest.getKeyword())) {
            String keyword = queryRequest.getKeyword().trim();
            queryWrapper.where(SysTenant::getTenantCode).like(keyword).or(SysTenant::getTenantName).like(keyword);
            log.debug("使用关键词搜索: {}", keyword);
        } else {
            // 如果没有keyword，则使用具体字段查询
            if (StrUtil.isNotBlank(queryRequest.getTenantCode())) {
                queryWrapper.like(SysTenant::getTenantCode, queryRequest.getTenantCode().trim());
            }
            if (StrUtil.isNotBlank(queryRequest.getTenantName())) {
                queryWrapper.like(SysTenant::getTenantName, queryRequest.getTenantName().trim());
            }
        }
        
        if (StrUtil.isNotBlank(queryRequest.getContactName())) {
            queryWrapper.like(SysTenant::getContactName, queryRequest.getContactName().trim());
        }
        if (StrUtil.isNotBlank(queryRequest.getContactPhone())) {
            queryWrapper.like(SysTenant::getContactPhone, queryRequest.getContactPhone().trim());
        }
        if (StrUtil.isNotBlank(queryRequest.getContactEmail())) {
            queryWrapper.like(SysTenant::getContactEmail, queryRequest.getContactEmail().trim());
        }
        if (queryRequest.getStatus() != null) {
            queryWrapper.eq(SysTenant::getStatus, queryRequest.getStatus());
        }
        if (queryRequest.getCreateTimeStart() != null) {
            queryWrapper.ge(SysTenant::getCreateTime, queryRequest.getCreateTimeStart());
        }
        if (queryRequest.getCreateTimeEnd() != null) {
            queryWrapper.le(SysTenant::getCreateTime, queryRequest.getCreateTimeEnd());
        }

        // 处理过期状态查询
        if (queryRequest.getExpired() != null) {
            if (queryRequest.getExpired()) {
                // 查询已过期的租户
                queryWrapper.and(SysTenant::getExpireTime).isNotNull()
                           .and(SysTenant::getExpireTime).lt(LocalDateTime.now());
            } else {
                // 查询未过期的租户（包括永久有效的）
                queryWrapper.and(SysTenant::getExpireTime).isNull()
                           .or(SysTenant::getExpireTime).ge(LocalDateTime.now());
            }
        }
        
        // 用户数量限制范围查询
        if (queryRequest.getUserLimitMin() != null) {
            queryWrapper.ge(SysTenant::getMaxUserCount, queryRequest.getUserLimitMin());
        }
        if (queryRequest.getUserLimitMax() != null) {
            queryWrapper.le(SysTenant::getMaxUserCount, queryRequest.getUserLimitMax());
        }

        // 排序
        queryWrapper.orderBy(SysTenant::getCreateTime, false);

        // 分页查询
        Page<SysTenant> page = tenantMapper.paginate(
                Page.of(queryRequest.getPageNum(), queryRequest.getPageSize()),
                queryWrapper
        );

        log.debug("查询到租户数量: {}", page.getTotalRow());
        return page;
    }

    @Override
    public List<SysTenant> getAllEnabledTenants() {
        log.debug("获取所有启用的租户");
        
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(SysTenant::getDeleted).eq(0)
                .and(SysTenant::getStatus).eq(1)
                .orderBy(SysTenant::getTenantName, true);

        List<SysTenant> tenants = tenantMapper.selectListByQuery(queryWrapper);
        log.debug("查询到启用租户数量: {}", tenants.size());
        return tenants;
    }

    @Override
    public SysTenant getTenantById(Long id) {
        log.debug("根据ID获取租户详情: {}", id);
        
        SysTenant tenant = tenantMapper.selectOneById(id);
        if (tenant != null && tenant.getDeleted() == 0) {
            return tenant;
        }
        return null;
    }

    @Override
    public SysTenant getTenantByCode(String tenantCode) {
        log.debug("根据租户编码获取租户信息: {}", tenantCode);
        
        return tenantMapper.selectByTenantCode(tenantCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysTenant createTenant(SysTenant tenant) {
        log.debug("创建租户: {}", tenant);
        
        // 检查租户编码是否已存在
        if (checkTenantCodeExists(tenant.getTenantCode(), null)) {
            throw new RuntimeException("租户编码已存在: " + tenant.getTenantCode());
        }
        
        // 设置默认值
        if (tenant.getStatus() == null) {
            tenant.setStatus(1); // 默认启用
        }
        if (tenant.getMaxUserCount() == null) {
            tenant.setMaxUserCount(100); // 默认100个用户
        }
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        tenant.setCreateTime(now);
        tenant.setUpdateTime(now);
        tenant.setDeleted(0);
        tenant.setVersion(0);
        
        tenantMapper.insert(tenant);
        log.debug("租户创建成功，ID: {}", tenant.getId());
        
        return tenant;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysTenant updateTenant(SysTenant tenant) {
        log.debug("更新租户: {}", tenant);
        
        // 检查租户是否存在
        SysTenant existingTenant = getTenantById(tenant.getId());
        if (existingTenant == null) {
            throw new RuntimeException("租户不存在: " + tenant.getId());
        }
        
        // 设置更新时间
        tenant.setUpdateTime(LocalDateTime.now());
        
        tenantMapper.update(tenant);
        log.debug("租户更新成功，ID: {}", tenant.getId());
        
        return getTenantById(tenant.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTenant(Long id) {
        log.debug("删除租户: {}", id);
        
        // 检查租户是否存在
        SysTenant tenant = getTenantById(id);
        if (tenant == null) {
            throw new RuntimeException("租户不存在: " + id);
        }
        
        // 逻辑删除
        SysTenant updateTenant = new SysTenant();
        updateTenant.setId(id);
        updateTenant.setDeleted(1);
        updateTenant.setUpdateTime(LocalDateTime.now());
        
        int result = tenantMapper.update(updateTenant);
        log.debug("租户删除结果: {}", result > 0 ? "成功" : "失败");
        
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTenants(List<Long> ids) {
        log.debug("批量删除租户: {}", ids);
        
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        int successCount = 0;
        for (Long id : ids) {
            try {
                if (deleteTenant(id)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("删除租户失败，ID: {}, 错误: {}", id, e.getMessage());
            }
        }
        
        log.debug("批量删除租户完成，成功: {}, 总数: {}", successCount, ids.size());
        return successCount == ids.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableTenant(Long id) {
        log.debug("启用租户: {}", id);
        return updateTenantStatus(id, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableTenant(Long id) {
        log.debug("禁用租户: {}", id);
        return updateTenantStatus(id, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTenantStatus(Long id, Integer status) {
        log.debug("更新租户状态: {}, 状态: {}", id, status);
        
        // 检查租户是否存在
        SysTenant tenant = getTenantById(id);
        if (tenant == null) {
            throw new RuntimeException("租户不存在: " + id);
        }
        
        SysTenant updateTenant = new SysTenant();
        updateTenant.setId(id);
        updateTenant.setStatus(status);
        updateTenant.setUpdateTime(LocalDateTime.now());
        
        int result = tenantMapper.update(updateTenant);
        log.debug("租户状态更新结果: {}", result > 0 ? "成功" : "失败");
        
        return result > 0;
    }

    @Override
    public boolean checkTenantCodeExists(String tenantCode, Long excludeId) {
        log.debug("检查租户编码是否存在: {}, 排除ID: {}", tenantCode, excludeId);
        
        int count = tenantMapper.checkTenantCodeExists(tenantCode, excludeId);
        boolean exists = count > 0;
        
        log.debug("租户编码检查结果: {}", exists ? "存在" : "不存在");
        return exists;
    }

    @Override
    public TenantStatsDTO getTenantStats(Long tenantId) {
        log.debug("获取租户统计信息: {}", tenantId);

        // 检查租户是否存在
        SysTenant tenant = getTenantById(tenantId);
        if (tenant == null) {
            throw new RuntimeException("租户不存在: " + tenantId);
        }

        // 构建统计信息
        TenantStatsDTO stats = new TenantStatsDTO();
        stats.setTenantId(tenantId);
        stats.setDataTime(LocalDateTime.now());

        // TODO: 实现真实的统计数据查询
        // 这里需要查询其他表来获取统计数据
        // 暂时返回模拟数据
        stats.setUserCount(25);
        stats.setRoleCount(5);
        stats.setDeptCount(8);
        stats.setStorageUsed(128);
        stats.setActiveUserCount(18);
        stats.setTodayLoginCount(6);
        stats.setMonthlyNewUserCount(3);
        stats.setLastLoginTime(LocalDateTime.now().minusHours(2));

        log.debug("租户统计信息: {}", stats);
        return stats;
    }

    @Override
    public List<TenantConfigDTO> getTenantConfigs(Long tenantId) {
        log.debug("获取租户配置: {}", tenantId);

        try {
            // 查询租户配置
            List<SysTenantConfig> configs = tenantConfigMapper.selectListByQuery(
                QueryWrapper.create()
                    .eq(SysTenantConfig::getTenantId, tenantId)
                    .eq(SysTenantConfig::getDeleted, 0)
                    .orderBy(SysTenantConfig::getSortOrder, true)
                    .orderBy(SysTenantConfig::getCreateTime, false)
            );

            // 转换为DTO
            return configs.stream()
                .map(this::convertToConfigDTO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取租户配置失败: {}", tenantId, e);
            throw new RuntimeException("获取租户配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTenantConfigs(Long tenantId, List<TenantConfigDTO> configs) {
        log.debug("更新租户配置: {}, 配置数量: {}", tenantId, configs.size());

        try {
            // 验证租户是否存在
            SysTenant tenant = this.getTenantById(tenantId);
            if (tenant == null) {
                throw new RuntimeException("租户不存在");
            }

            // 批量更新配置
            for (TenantConfigDTO configDTO : configs) {
                SysTenantConfig config = convertToConfigEntity(configDTO);
                config.setTenantId(tenantId);

                if (config.getId() != null) {
                    // 更新现有配置
                    tenantConfigMapper.update(config);
                } else {
                    // 新增配置
                    tenantConfigMapper.insert(config);
                }
            }

            log.info("租户配置更新成功: {}", tenantId);
            return true;

        } catch (Exception e) {
            log.error("更新租户配置失败: {}", tenantId, e);
            throw new RuntimeException("更新租户配置失败: " + e.getMessage());
        }
    }

    @Override
    public List<TenantUserDTO> getTenantUsers(Long tenantId) {
        log.debug("获取租户用户列表: {}", tenantId);

        try {
            // 验证租户是否存在
            SysTenant tenant = this.getTenantById(tenantId);
            if (tenant == null) {
                throw new RuntimeException("租户不存在");
            }

            // 获取租户用户列表（包含用户详细信息）
            return tenantUserMapper.getTenantUsersWithDetails(tenantId);

        } catch (Exception e) {
            log.error("获取租户用户列表失败: {}", tenantId, e);
            throw new RuntimeException("获取租户用户列表失败: " + e.getMessage());
        }
    }

    @Override
    public List<TenantUserDTO> getAvailableUsersForTenant(Long tenantId) {
        log.debug("获取可分配给租户的用户列表: {}", tenantId);

        try {
            // 验证租户是否存在
            SysTenant tenant = this.getTenantById(tenantId);
            if (tenant == null) {
                throw new RuntimeException("租户不存在");
            }

            // 获取可分配用户列表（排除已分配的用户）
            return tenantUserMapper.getAvailableUsersForTenant(tenantId);

        } catch (Exception e) {
            log.error("获取可分配用户列表失败: {}", tenantId, e);
            throw new RuntimeException("获取可分配用户列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignUsersToTenant(Long tenantId, List<Long> userIds) {
        log.debug("分配用户到租户: {}, 用户数量: {}", tenantId, userIds.size());

        try {
            // 验证租户是否存在
            SysTenant tenant = this.getTenantById(tenantId);
            if (tenant == null) {
                throw new RuntimeException("租户不存在");
            }

            // 批量分配用户
            for (Long userId : userIds) {
                // 检查用户是否已经分配给该租户
                long count = tenantUserMapper.selectCountByQuery(
                    QueryWrapper.create()
                        .eq(SysTenantUser::getTenantId, tenantId)
                        .eq(SysTenantUser::getUserId, userId)
                        .eq(SysTenantUser::getDeleted, 0)
                );

                if (count == 0) {
                    // 创建租户用户关联
                    SysTenantUser tenantUser = new SysTenantUser();
                    tenantUser.setTenantId(tenantId);
                    tenantUser.setUserId(userId);
                    tenantUser.setUserRole(2); // 默认为普通用户
                    tenantUser.setStatus(1); // 默认启用
                    tenantUser.setAssignTime(LocalDateTime.now());

                    tenantUserMapper.insert(tenantUser);
                    log.debug("用户 {} 已分配到租户 {}", userId, tenantId);
                } else {
                    log.debug("用户 {} 已经分配给租户 {}", userId, tenantId);
                }
            }

            log.info("用户分配到租户成功: {}, 用户数量: {}", tenantId, userIds.size());
            return true;

        } catch (Exception e) {
            log.error("分配用户到租户失败: {}", tenantId, e);
            throw new RuntimeException("分配用户到租户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserFromTenant(Long tenantId, Long userId) {
        log.debug("从租户移除用户: {}, 用户ID: {}", tenantId, userId);

        try {
            // 逻辑删除租户用户关联
            SysTenantUser updateEntity = new SysTenantUser();
            updateEntity.setDeleted(1);

            int count = tenantUserMapper.updateByQuery(
                updateEntity,
                QueryWrapper.create()
                    .eq(SysTenantUser::getTenantId, tenantId)
                    .eq(SysTenantUser::getUserId, userId)
                    .eq(SysTenantUser::getDeleted, 0)
            );

            log.info("从租户移除用户成功: {}, 用户ID: {}, 影响行数: {}", tenantId, userId, count);
            return count > 0;

        } catch (Exception e) {
            log.error("从租户移除用户失败: {}, 用户ID: {}", tenantId, userId, e);
            throw new RuntimeException("从租户移除用户失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] exportTenants(List<Long> tenantIds) {
        log.debug("导出租户数据: {}", tenantIds);

        // TODO: 实现租户数据导出
        return new byte[0];
    }

    /**
     * 转换配置实体为DTO
     */
    private TenantConfigDTO convertToConfigDTO(SysTenantConfig config) {
        TenantConfigDTO dto = new TenantConfigDTO();
        dto.setId(config.getId());
        dto.setTenantId(config.getTenantId());
        dto.setConfigKey(config.getConfigKey());
        dto.setConfigValue(config.getConfigValue());
        dto.setConfigName(config.getConfigName());
        dto.setConfigDesc(config.getConfigDesc());
        dto.setConfigType(config.getConfigType());
        dto.setIsSystem(config.getIsSystem());
        dto.setSortOrder(config.getSortOrder());
        dto.setStatus(config.getStatus());
        dto.setCreateTime(config.getCreateTime());
        dto.setUpdateTime(config.getUpdateTime());
        return dto;
    }

    /**
     * 转换DTO为配置实体
     */
    private SysTenantConfig convertToConfigEntity(TenantConfigDTO dto) {
        SysTenantConfig config = new SysTenantConfig();
        config.setId(dto.getId());
        config.setTenantId(dto.getTenantId());
        config.setConfigKey(dto.getConfigKey());
        config.setConfigValue(dto.getConfigValue());
        config.setConfigName(dto.getConfigName());
        config.setConfigDesc(dto.getConfigDesc());
        config.setConfigType(dto.getConfigType());
        config.setIsSystem(dto.getIsSystem());
        config.setSortOrder(dto.getSortOrder());
        config.setStatus(dto.getStatus());
        return config;
    }
}
