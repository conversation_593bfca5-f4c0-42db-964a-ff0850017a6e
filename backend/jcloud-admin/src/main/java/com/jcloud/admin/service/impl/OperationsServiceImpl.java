package com.jcloud.admin.service.impl;

import com.jcloud.admin.constant.OperationsConstants;
import com.jcloud.admin.dto.request.AnchorQueryRequest;
import com.jcloud.admin.dto.request.ConsumeQueryRequest;
import com.jcloud.admin.dto.request.RechargeQueryRequest;
import com.jcloud.admin.dto.request.SubUserQueryRequest;
import com.jcloud.admin.dto.response.*;
import com.jcloud.admin.mapper.OperationsMapper;
import com.jcloud.admin.service.OperationsService;
import com.jcloud.common.annotation.DataSource;
import com.jcloud.common.config.DataSourceContextHolder;
import com.jcloud.common.dto.OperationsStatsResponse;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.util.DataMaskingUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;

/**
 * 运营数据服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DataSource("slave")  // 统一使用从库数据源进行运营数据查询
public class OperationsServiceImpl implements OperationsService {

    private final OperationsMapper operationsMapper;

    /**
     * 确保整个方法使用slave数据源
     *
     * @param request 查询请求参数
     * @return 分页的主播列表
     */
    @Override
    @DataSource("slave")
    public PageResult<AnchorListResponse> getAnchorList(AnchorQueryRequest request) {
        log.info("开始查询主播列表，查询条件：{}", request);

        try {
            // 参数校验
            validatePageQuery(request);

            log.debug("开始手动分页查询：pageNum={}, pageSize={}, offset={}",
                request.getPageNum(), request.getPageSize(), request.getOffset());

            // 先查询总记录数
            Long totalCount = operationsMapper.countAnchorList(request);
            log.debug("查询到总记录数：{}", totalCount);

            // 如果总记录数为0，直接返回空结果
            if (totalCount == null || totalCount == 0) {
                log.info("主播列表查询完成，总记录数为0");
                return PageResult.of(List.of(), 0L, request.getPageNum(), request.getPageSize());
            }

            // 查询当前页数据
            List<AnchorListResponse> records = operationsMapper.selectAnchorList(request);
            log.debug("查询到当前页记录数：{}", records != null ? records.size() : 0);

            // 处理null结果
            if (records == null) {
                log.warn("分页查询记录列表为null，返回空结果");
                records = List.of();
            }

            // 数据处理
            processAnchorListData(records);

            log.info("主播列表查询完成，当前页记录数：{}，总记录数：{}", records.size(), totalCount);
            return PageResult.of(records, totalCount, request.getPageNum(), request.getPageSize());

        } catch (DataAccessException e) {
            log.error("查询主播列表时发生数据库异常", e);
            throw new BusinessException("查询主播列表失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("查询主播列表时发生未知异常", e);
            throw new BusinessException("查询主播列表失败");
        }
    }

    @Override
    public AnchorStatsResponse getAnchorStats(Integer anchorId, Integer startTime, Integer endTime) {
        log.info("开始获取主播统计数据，主播ID：{}，时间范围：{} - {}", anchorId, startTime, endTime);

        try {
            // 参数校验
            if (anchorId == null || anchorId <= 0) {
                throw new BusinessException("主播ID不能为空");
            }

            // 调用存储过程获取统计数据
            AnchorStatsResponse stats = operationsMapper.getAnchorStatistics(anchorId, startTime, endTime);

            if (stats == null) {
                log.warn("未找到主播ID为 {} 的统计数据", anchorId);
                return createEmptyAnchorStats(startTime, endTime);
            }

            // 数据处理和计算
            processAnchorStatsData(stats);

            log.info("主播统计数据获取完成，主播ID：{}", anchorId);
            return stats;

        } catch (DataAccessException e) {
            log.error("获取主播统计数据时发生数据库异常，主播ID：{}", anchorId, e);
            throw new BusinessException("获取主播统计数据失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("获取主播统计数据时发生未知异常，主播ID：{}", anchorId, e);
            throw new BusinessException("获取主播统计数据失败");
        }
    }

    @Override
    public FirstRechargeStatsResponse getFirstRechargeStats(Integer anchorId, Integer startTime, Integer endTime) {
        log.info("开始获取主播首充统计数据，主播ID：{}，时间范围：{} - {}", anchorId, startTime, endTime);

        try {
            // 参数校验
            if (anchorId == null || anchorId <= 0) {
                throw new BusinessException("主播ID不能为空");
            }

            // 获取首充统计数据
            FirstRechargeStatsResponse stats = operationsMapper.getFirstRechargeStats(anchorId, startTime, endTime);

            if (stats == null) {
                log.warn("未找到主播ID为 {} 的首充统计数据", anchorId);
                return createEmptyFirstRechargeStats(anchorId, startTime, endTime);
            }

            // 数据处理
            processFirstRechargeStatsData(stats);

            log.info("主播首充统计数据获取完成，主播ID：{}", anchorId);
            return stats;

        } catch (DataAccessException e) {
            log.error("获取主播首充统计数据时发生数据库异常，主播ID：{}", anchorId, e);
            throw new BusinessException("获取主播首充统计数据失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("获取主播首充统计数据时发生未知异常，主播ID：{}", anchorId, e);
            throw new BusinessException("获取主播首充统计数据失败");
        }
    }

    @Override
    public PageResult<SubUserResponse> getSubUsers(Integer anchorId, SubUserQueryRequest request) {
        log.info("开始查询主播下级用户列表，主播ID：{}，查询条件：{}", anchorId, request);

        try {
            // 参数校验
            if (anchorId == null || anchorId <= 0) {
                throw new BusinessException("主播ID不能为空");
            }
            validatePageQuery(request);

            log.debug("开始手动分页查询下级用户：主播ID={}, pageNum={}, pageSize={}, offset={}",
                anchorId, request.getPageNum(), request.getPageSize(), request.getOffset());

            // 先查询总记录数
            Long totalCount = operationsMapper.countSubUsers(anchorId, request);
            log.debug("查询到下级用户总记录数：{}", totalCount);

            // 如果总记录数为0，直接返回空结果
            if (totalCount == null || totalCount == 0) {
                log.info("主播下级用户列表查询完成，主播ID：{}，总记录数为0", anchorId);
                return PageResult.of(List.of(), 0L, request.getPageNum(), request.getPageSize());
            }

            // 查询当前页数据
            List<SubUserResponse> records = operationsMapper.selectSubUsers(anchorId, request);
            log.debug("查询到下级用户当前页记录数：{}", records != null ? records.size() : 0);

            // 处理null结果
            if (records == null) {
                log.warn("分页查询记录列表为null，主播ID：{}，返回空结果", anchorId);
                records = List.of();
            }

            // 数据处理
            processSubUserData(records);

            log.info("主播下级用户列表查询完成，主播ID：{}，当前页记录数：{}，总记录数：{}",
                anchorId, records.size(), totalCount);
            return PageResult.of(records, totalCount, request.getPageNum(), request.getPageSize());

        } catch (DataAccessException e) {
            log.error("查询主播下级用户列表时发生数据库异常，主播ID：{}", anchorId, e);
            throw new BusinessException("查询主播下级用户列表失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("查询主播下级用户列表时发生未知异常，主播ID：{}", anchorId, e);
            throw new BusinessException("查询主播下级用户列表失败");
        } finally {
            // 确保清除数据源设置
            DataSourceContextHolder.clearDataSource();
        }
    }

    @Override
    public PageResult<ConsumeDetailResponse> getUserConsumeDetails(Integer userId, ConsumeQueryRequest request) {
        log.info("开始查询用户消费详情，用户ID：{}，查询条件：{}", userId, request);

        try {
            // 参数校验
            if (userId == null || userId <= 0) {
                throw new BusinessException("用户ID不能为空");
            }
            validatePageQuery(request);

            log.debug("开始手动分页查询消费详情：用户ID={}, pageNum={}, pageSize={}, offset={}",
                userId, request.getPageNum(), request.getPageSize(), request.getOffset());

            // 先查询总记录数
            Long totalCount = operationsMapper.countConsumeDetails(userId, request);
            log.debug("查询到消费详情总记录数：{}", totalCount);

            // 如果总记录数为0，直接返回空结果
            if (totalCount == null || totalCount == 0) {
                log.info("用户消费详情查询完成，用户ID：{}，总记录数为0", userId);
                return PageResult.of(List.of(), 0L, request.getPageNum(), request.getPageSize());
            }

            // 查询当前页数据
            List<ConsumeDetailResponse> records = operationsMapper.selectConsumeDetails(userId, request);
            log.debug("查询到消费详情当前页记录数：{}", records != null ? records.size() : 0);

            // 处理null结果
            if (records == null) {
                log.warn("分页查询记录列表为null，用户ID：{}，返回空结果", userId);
                records = List.of();
            }

            // 数据处理
            processConsumeDetailData(records);

            log.info("用户消费详情查询完成，用户ID：{}，当前页记录数：{}，总记录数：{}",
                userId, records.size(), totalCount);
            return PageResult.of(records, totalCount, request.getPageNum(), request.getPageSize());

        } catch (DataAccessException e) {
            log.error("查询用户消费详情时发生数据库异常，用户ID：{}", userId, e);
            throw new BusinessException("查询用户消费详情失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("查询用户消费详情时发生未知异常，用户ID：{}", userId, e);
            throw new BusinessException("查询用户消费详情失败");
        }
    }

    @Override
    public PageResult<RechargeDetailResponse> getUserRechargeDetails(Integer userId, RechargeQueryRequest request) {
        log.info("开始查询用户充值详情，用户ID：{}，查询条件：{}", userId, request);

        try {
            // 参数校验
            if (userId == null || userId <= 0) {
                throw new BusinessException("用户ID不能为空");
            }
            validatePageQuery(request);

            log.debug("开始手动分页查询充值详情：用户ID={}, pageNum={}, pageSize={}, offset={}",
                userId, request.getPageNum(), request.getPageSize(), request.getOffset());

            // 先查询总记录数
            Long totalCount = operationsMapper.countRechargeDetails(userId, request);
            log.debug("查询到充值详情总记录数：{}", totalCount);

            // 如果总记录数为0，直接返回空结果
            if (totalCount == null || totalCount == 0) {
                log.info("用户充值详情查询完成，用户ID：{}，总记录数为0", userId);
                return PageResult.of(List.of(), 0L, request.getPageNum(), request.getPageSize());
            }

            // 查询当前页数据
            List<RechargeDetailResponse> records = operationsMapper.selectRechargeDetails(userId, request);
            log.debug("查询到充值详情当前页记录数：{}", records != null ? records.size() : 0);

            // 处理null结果
            if (records == null) {
                log.warn("分页查询记录列表为null，用户ID：{}，返回空结果", userId);
                records = List.of();
            }

            // 数据处理
            processRechargeDetailData(records);

            log.info("用户充值详情查询完成，用户ID：{}，当前页记录数：{}，总记录数：{}",
                userId, records.size(), totalCount);
            return PageResult.of(records, totalCount, request.getPageNum(), request.getPageSize());

        } catch (DataAccessException e) {
            log.error("查询用户充值详情时发生数据库异常，用户ID：{}", userId, e);
            throw new BusinessException("查询用户充值详情失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("查询用户充值详情时发生未知异常，用户ID：{}", userId, e);
            throw new BusinessException("查询用户充值详情失败");
        }
    }

    /**
     * 校验分页查询参数
     */
    private void validatePageQuery(com.jcloud.common.page.PageQuery pageQuery) {
        if (pageQuery.getPageNum() == null || pageQuery.getPageNum() < 1) {
            pageQuery.setPageNum(OperationsConstants.Page.DEFAULT_PAGE_NUM);
        }
        if (pageQuery.getPageSize() == null || pageQuery.getPageSize() < 1) {
            pageQuery.setPageSize(OperationsConstants.Page.DEFAULT_PAGE_SIZE);
        }
        if (pageQuery.getPageSize() > OperationsConstants.Page.MAX_PAGE_SIZE) {
            pageQuery.setPageSize(OperationsConstants.Page.MAX_PAGE_SIZE);
        }
    }

    /**
     * 处理主播列表数据
     */
    private void processAnchorListData(List<AnchorListResponse> anchors) {
        if (anchors == null || anchors.isEmpty()) {
            return;
        }

        for (AnchorListResponse anchor : anchors) {
            // 确保字符串字段不为null并进行脱敏处理
            if (anchor.getNickname() == null) {
                anchor.setNickname("未设置昵称");
            }
            if (anchor.getUsername() == null) {
                anchor.setUsername("未设置用户名");
            }
            if (anchor.getPhone() == null) {
                anchor.setPhone("未绑定手机");
            } else {
                // 对主播手机号进行脱敏处理
                anchor.setPhone(DataMaskingUtils.maskPhone(anchor.getPhone()));
            }
            if (anchor.getUserimage() == null) {
                anchor.setUserimage("");
            }
            if (anchor.getInviteCode() == null) {
                anchor.setInviteCode("");
            }
            if (anchor.getLastLoginIp() == null) {
                anchor.setLastLoginIp("");
            }

            // 确保数值字段不为null
            if (anchor.getCoin() == null) {
                anchor.setCoin(BigDecimal.ZERO);
            }
            if (anchor.getKey() == null) {
                anchor.setKey(BigDecimal.ZERO);
            }
            if (anchor.getSubUserCount() == null) {
                anchor.setSubUserCount(0);
            }
        }
    }

    /**
     * 处理主播统计数据
     */
    private void processAnchorStatsData(AnchorStatsResponse stats) {
        if (stats == null) {
            return;
        }

        // 确保数值字段不为null
        if (stats.getTotalRecharge() == null) {
            stats.setTotalRecharge(BigDecimal.ZERO);
        }
        if (stats.getTotalConsume() == null) {
            stats.setTotalConsume(BigDecimal.ZERO);
        }
        if (stats.getTotalClaimAmount() == null) {
            stats.setTotalClaimAmount(BigDecimal.ZERO);
        }
        if (stats.getTotalShippedAmount() == null) {
            stats.setTotalShippedAmount(BigDecimal.ZERO);
        }
        if (stats.getTotalBackpackAmount() == null) {
            stats.setTotalBackpackAmount(BigDecimal.ZERO);
        }
        if (stats.getPeriodTotalRecharge() == null) {
            stats.setPeriodTotalRecharge(BigDecimal.ZERO);
        }
        if (stats.getTotalTurnover() == null) {
            stats.setTotalTurnover(BigDecimal.ZERO);
        }

        // 计算实际利润和利润比
        calculateProfitData(stats);
    }

    /**
     * 计算利润相关数据
     */
    private void calculateProfitData(AnchorStatsResponse stats) {
        BigDecimal totalConsume = stats.getTotalConsume();
        BigDecimal totalShipped = stats.getTotalShippedAmount();

        if (totalConsume != null && totalShipped != null) {
            // 实际利润 = 总消费金额 - 总实际发货金额
            BigDecimal actualProfit = totalConsume.subtract(totalShipped);
            stats.setActualProfit(actualProfit);

            // 利润比 = 实际利润 / 总消费金额 × 100%
            if (totalConsume.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal profitRatio = actualProfit.divide(totalConsume, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                stats.setProfitRatio(profitRatio);
            } else {
                stats.setProfitRatio(BigDecimal.ZERO);
            }
        }
    }

    /**
     * 处理首充统计数据
     */
    private void processFirstRechargeStatsData(FirstRechargeStatsResponse stats) {
        if (stats == null) {
            return;
        }

        // 确保数值字段不为null
        if (stats.getFirstRechargeUserCount() == null) {
            stats.setFirstRechargeUserCount(0);
        }
        if (stats.getTotalSubUserCount() == null) {
            stats.setTotalSubUserCount(0);
        }
        if (stats.getTotalFirstRechargeAmount() == null) {
            stats.setTotalFirstRechargeAmount(BigDecimal.ZERO);
        }
        if (stats.getPeriodFirstRechargeAmount() == null) {
            stats.setPeriodFirstRechargeAmount(BigDecimal.ZERO);
        }

        // 重新计算转化率和平均金额，确保精度
        recalculateFirstRechargeStats(stats);
    }

    /**
     * 重新计算首充统计数据
     */
    private void recalculateFirstRechargeStats(FirstRechargeStatsResponse stats) {
        // 计算首充转化率
        if (stats.getTotalSubUserCount() > 0) {
            BigDecimal conversionRate = new BigDecimal(stats.getFirstRechargeUserCount())
                    .divide(new BigDecimal(stats.getTotalSubUserCount()), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            stats.setFirstRechargeConversionRate(conversionRate);
        } else {
            stats.setFirstRechargeConversionRate(BigDecimal.ZERO);
        }

        // 计算平均首充金额
        if (stats.getFirstRechargeUserCount() > 0) {
            BigDecimal avgAmount = stats.getTotalFirstRechargeAmount()
                    .divide(new BigDecimal(stats.getFirstRechargeUserCount()), 2, RoundingMode.HALF_UP);
            stats.setAvgFirstRechargeAmount(avgAmount);
        } else {
            stats.setAvgFirstRechargeAmount(BigDecimal.ZERO);
        }

        // 计算时间区间内平均首充金额
        if (stats.getPeriodFirstRechargeUserCount() != null && stats.getPeriodFirstRechargeUserCount() > 0) {
            BigDecimal periodAvgAmount = stats.getPeriodFirstRechargeAmount()
                    .divide(new BigDecimal(stats.getPeriodFirstRechargeUserCount()), 2, RoundingMode.HALF_UP);
            stats.setPeriodAvgFirstRechargeAmount(periodAvgAmount);
        } else {
            stats.setPeriodAvgFirstRechargeAmount(BigDecimal.ZERO);
        }
    }

    /**
     * 处理下级用户数据
     */
    private void processSubUserData(List<SubUserResponse> users) {
        if (users == null || users.isEmpty()) {
            return;
        }

        for (SubUserResponse user : users) {
            // 确保字符串字段不为null
            if (user.getNickname() == null) {
                user.setNickname("未设置昵称");
            }
            if (user.getUsername() == null) {
                user.setUsername("未设置用户名");
            }
            if (user.getPhone() == null) {
                user.setPhone("未绑定手机");
            } else {
                // 对手机号进行脱敏处理
                user.setPhone(DataMaskingUtils.maskPhone(user.getPhone()));
            }
            if (user.getUserimage() == null) {
                user.setUserimage("");
            }

            // 确保数值字段不为null
            if (user.getCoin() == null) {
                user.setCoin(BigDecimal.ZERO);
            }
            if (user.getKey() == null) {
                user.setKey(BigDecimal.ZERO);
            }
            if (user.getTotalRecharge() == null) {
                user.setTotalRecharge(BigDecimal.ZERO);
            }
            if (user.getTotalConsume() == null) {
                user.setTotalConsume(BigDecimal.ZERO);
            }
            if (user.getFirstRechargeAmount() == null) {
                user.setFirstRechargeAmount(BigDecimal.ZERO);
            }
            if (user.getHasFirstRecharge() == null) {
                user.setHasFirstRecharge(false);
            }

            // 确保整数字段不为null
            if (user.getState() == null) {
                user.setState(1); // 默认正常状态
            }
            if (user.getIsauth() == null) {
                user.setIsauth(0); // 默认未实名
            }
            if (user.getLevel() == null) {
                user.setLevel(1); // 默认等级1
            }
            if (user.getExp() == null) {
                user.setExp(BigDecimal.ZERO); // 默认经验0
            }
        }
    }

    /**
     * 处理消费详情数据
     */
    private void processConsumeDetailData(List<ConsumeDetailResponse> details) {
        if (details == null || details.isEmpty()) {
            return;
        }

        for (ConsumeDetailResponse detail : details) {
            // 确保字符串字段不为null
            if (detail.getInfo() == null) {
                detail.setInfo("无消费说明");
            }

            // 推断消费类型
            if (detail.getConsumeType() == null) {
                String consumeType = OperationsConstants.ConsumeType.inferType(detail.getInfo());
                detail.setConsumeType(consumeType);
            }

            // 确保数值字段不为null
            if (detail.getAmount() == null) {
                detail.setAmount(BigDecimal.ZERO);
            }
            if (detail.getBalance() == null) {
                detail.setBalance(BigDecimal.ZERO);
            }
            if (detail.getIsAfterFirstRecharge() == null) {
                detail.setIsAfterFirstRecharge(false);
            }

            // 确保时间字段不为null
            if (detail.getTime() == null) {
                detail.setTime(0); // 设置为0时间戳
            }
        }
    }

    /**
     * 处理充值详情数据
     */
    private void processRechargeDetailData(List<RechargeDetailResponse> details) {
        if (details == null || details.isEmpty()) {
            return;
        }

        for (RechargeDetailResponse detail : details) {
            // 设置状态描述
            if (detail.getStateDesc() == null) {
                String stateDesc = OperationsConstants.RechargeState.getStateDesc(detail.getState());
                detail.setStateDesc(stateDesc);
            }

            // 推断支付方式
            if (detail.getPaymentMethod() == null) {
                String paymentMethod = OperationsConstants.PaymentMethod.inferMethod(detail.getPayid());
                detail.setPaymentMethod(paymentMethod);
            }

            // 确保数值字段不为null
            if (detail.getAmount() == null) {
                detail.setAmount(BigDecimal.ZERO);
            }
            if (detail.getCoin() == null) {
                detail.setCoin(BigDecimal.ZERO);
            }
            if (detail.getIsFirstRecharge() == null) {
                detail.setIsFirstRecharge(false);
            }
        }
    }

    /**
     * 创建空的主播统计数据
     */
    private AnchorStatsResponse createEmptyAnchorStats(Integer startTime, Integer endTime) {
        AnchorStatsResponse stats = new AnchorStatsResponse();
        stats.setTotalRecharge(BigDecimal.ZERO);
        stats.setTotalConsume(BigDecimal.ZERO);
        stats.setUserCount(0);
        stats.setPeriodNewUserCount(0);
        stats.setTotalClaimAmount(BigDecimal.ZERO);
        stats.setTotalShippedAmount(BigDecimal.ZERO);
        stats.setTotalBackpackAmount(BigDecimal.ZERO);
        stats.setPeriodTotalRecharge(BigDecimal.ZERO);
        stats.setTotalTurnover(BigDecimal.ZERO);
        stats.setProfitRatio(BigDecimal.ZERO);
        stats.setActualProfit(BigDecimal.ZERO);
        stats.setStartTime(startTime);
        stats.setEndTime(endTime);
        return stats;
    }

    /**
     * 创建空的首充统计数据
     */
    private FirstRechargeStatsResponse createEmptyFirstRechargeStats(Integer anchorId, Integer startTime, Integer endTime) {
        FirstRechargeStatsResponse stats = new FirstRechargeStatsResponse();
        stats.setFirstRechargeUserCount(0);
        stats.setTotalSubUserCount(0);
        stats.setFirstRechargeConversionRate(BigDecimal.ZERO);
        stats.setTotalFirstRechargeAmount(BigDecimal.ZERO);
        stats.setAvgFirstRechargeAmount(BigDecimal.ZERO);
        stats.setPeriodFirstRechargeUserCount(0);
        stats.setPeriodFirstRechargeAmount(BigDecimal.ZERO);
        stats.setPeriodAvgFirstRechargeAmount(BigDecimal.ZERO);
        stats.setStartTime(startTime);
        stats.setEndTime(endTime);
        return stats;
    }

    @Override
    public OperationsStatsResponse getOperationsStats() {
        log.info("开始获取运营统计数据");

        try {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            YearMonth currentMonth = YearMonth.from(now);

            // 构建统计响应对象
            OperationsStatsResponse.OperationsStatsResponseBuilder builder = OperationsStatsResponse.builder()
                    .statsTime(now);

            // 1. 获取总主播数（复用现有查询逻辑）
            Long totalAnchors = getTotalAnchorsCount();
            builder.totalAnchors(totalAnchors);

            // 2. 获取活跃主播数（本月有业务的主播）
            Long activeAnchors = getActiveAnchorsCount(currentMonth);
            builder.activeAnchors(activeAnchors);

            // 3. 获取总用户数（所有主播的下级用户总数）
            Long totalUsers = getTotalUsersCount();
            builder.totalUsers(totalUsers);

            // 4. 获取本月新增主播数
            Long newAnchorsThisMonth = getNewAnchorsCount(currentMonth);
            builder.newAnchorsThisMonth(newAnchorsThisMonth);

            // 5. 获取本月新增用户数
            Long newUsersThisMonth = getNewUsersCount(currentMonth);
            builder.newUsersThisMonth(newUsersThisMonth);

            // 6. 获取本月总充值金额
            BigDecimal totalRechargeThisMonth = getTotalRechargeAmount(currentMonth);
            builder.totalRechargeThisMonth(totalRechargeThisMonth);

            // 7. 获取本月总消费金额
            BigDecimal totalConsumeThisMonth = getTotalConsumeAmount(currentMonth);
            builder.totalConsumeThisMonth(totalConsumeThisMonth);

            OperationsStatsResponse response = builder.build();

            log.info("运营统计数据获取完成: 总主播数={}, 活跃主播数={}, 总用户数={}",
                    totalAnchors, activeAnchors, totalUsers);

            return response;

        } catch (Exception e) {
            log.error("获取运营统计数据失败", e);
            throw new BusinessException("获取运营统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取总主播数
     * 复用现有的主播列表查询逻辑
     */
    private Long getTotalAnchorsCount() {
        try {
            AnchorQueryRequest request = new AnchorQueryRequest();
            request.setPageNum(1);
            request.setPageSize(1); // 只需要总数，不需要具体数据

            PageResult<AnchorListResponse> result = getAnchorList(request);
            return result.getTotal();
        } catch (Exception e) {
            log.warn("获取总主播数失败，返回默认值0", e);
            return 0L;
        }
    }

    /**
     * 获取活跃主播数（本月有业务的主播）
     */
    private Long getActiveAnchorsCount(YearMonth currentMonth) {
        try {
            return operationsMapper.getActiveAnchorsCount(
                    currentMonth.atDay(1).atStartOfDay(),
                    currentMonth.atEndOfMonth().atTime(23, 59, 59)
            );
        } catch (Exception e) {
            log.warn("获取活跃主播数失败，返回默认值0", e);
            return 0L;
        }
    }

    /**
     * 获取总用户数（所有主播的下级用户总数）
     */
    private Long getTotalUsersCount() {
        try {
            return operationsMapper.getTotalUsersCount();
        } catch (Exception e) {
            log.warn("获取总用户数失败，返回默认值0", e);
            return 0L;
        }
    }

    /**
     * 获取本月新增主播数
     */
    private Long getNewAnchorsCount(YearMonth currentMonth) {
        try {
            return operationsMapper.getNewAnchorsCount(
                    currentMonth.atDay(1).atStartOfDay(),
                    currentMonth.atEndOfMonth().atTime(23, 59, 59)
            );
        } catch (Exception e) {
            log.warn("获取本月新增主播数失败，返回默认值0", e);
            return 0L;
        }
    }

    /**
     * 获取本月新增用户数
     */
    private Long getNewUsersCount(YearMonth currentMonth) {
        try {
            return operationsMapper.getNewUsersCount(
                    currentMonth.atDay(1).atStartOfDay(),
                    currentMonth.atEndOfMonth().atTime(23, 59, 59)
            );
        } catch (Exception e) {
            log.warn("获取本月新增用户数失败，返回默认值0", e);
            return 0L;
        }
    }

    /**
     * 获取本月总充值金额
     */
    private BigDecimal getTotalRechargeAmount(YearMonth currentMonth) {
        try {
            BigDecimal amount = operationsMapper.getTotalRechargeAmount(
                    currentMonth.atDay(1).atStartOfDay(),
                    currentMonth.atEndOfMonth().atTime(23, 59, 59)
            );
            return amount != null ? amount : BigDecimal.ZERO;
        } catch (Exception e) {
            log.warn("获取本月总充值金额失败，返回默认值0", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取本月总消费金额
     */
    private BigDecimal getTotalConsumeAmount(YearMonth currentMonth) {
        try {
            BigDecimal amount = operationsMapper.getTotalConsumeAmount(
                    currentMonth.atDay(1).atStartOfDay(),
                    currentMonth.atEndOfMonth().atTime(23, 59, 59)
            );
            return amount != null ? amount : BigDecimal.ZERO;
        } catch (Exception e) {
            log.warn("获取本月总消费金额失败，返回默认值0", e);
            return BigDecimal.ZERO;
        }
    }
}
