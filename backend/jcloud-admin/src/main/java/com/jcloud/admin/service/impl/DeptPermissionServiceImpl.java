package com.jcloud.admin.service.impl;

import com.jcloud.admin.service.DeptPermissionService;
import com.jcloud.common.entity.SysDept;
import com.jcloud.common.entity.SysUserDept;
import com.jcloud.common.mapper.SysDeptMapper;
import com.jcloud.common.mapper.SysUserDeptMapper;
import com.jcloud.common.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门权限服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptPermissionServiceImpl implements DeptPermissionService {
    
    private final SysUserDeptMapper userDeptMapper;
    private final SysDeptMapper deptMapper;
    
    @Override
    @Cacheable(value = "userPrimaryDeptId", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public Long getUserPrimaryDeptId(Long userId) {
        if (userId == null) {
            return null;
        }
        
        Long tenantId = SecurityUtils.getTenantId();
        
        // 首先查找主部门
        Long primaryDeptId = userDeptMapper.selectPrimaryDeptIdByUserId(userId, tenantId);
        if (primaryDeptId != null) {
            log.debug("找到用户主部门: userId={}, deptId={}", userId, primaryDeptId);
            return primaryDeptId;
        }
        
        // 如果没有主部门，返回第一个部门
        Long firstDeptId = userDeptMapper.selectFirstDeptIdByUserId(userId, tenantId);
        if (firstDeptId != null) {
            log.debug("用户没有主部门，返回第一个部门: userId={}, deptId={}", userId, firstDeptId);
        } else {
            log.debug("用户没有关联任何部门: userId={}", userId);
        }
        
        return firstDeptId;
    }
    
    @Override
    @Cacheable(value = "userDeptIds", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public List<Long> getUserDeptIds(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        Long tenantId = SecurityUtils.getTenantId();
        return userDeptMapper.selectDeptIdsByUserId(userId, tenantId);
    }
    
    @Override
    public boolean isUsersInSameDept(Long userId1, Long userId2) {
        if (userId1 == null || userId2 == null) {
            return false;
        }
        
        if (userId1.equals(userId2)) {
            return true;
        }
        
        List<Long> user1Depts = getUserDeptIds(userId1);
        List<Long> user2Depts = getUserDeptIds(userId2);
        
        // 检查是否有交集
        return user1Depts.stream().anyMatch(user2Depts::contains);
    }
    
    @Override
    public boolean isUserInDept(Long userId, Long deptId) {
        if (userId == null || deptId == null) {
            return false;
        }
        
        Long tenantId = SecurityUtils.getTenantId();
        return userDeptMapper.existsByUserIdAndDeptId(userId, deptId, tenantId);
    }
    
    @Override
    @Cacheable(value = "deptAndSubDeptIds", key = "#deptId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public List<Long> getDeptAndSubDeptIds(Long deptId) {
        if (deptId == null) {
            return new ArrayList<>();
        }
        
        Set<Long> deptIds = new HashSet<>();
        deptIds.add(deptId);
        
        // 递归获取所有子部门
        collectSubDeptIds(deptId, deptIds);
        
        return new ArrayList<>(deptIds);
    }
    
    @Override
    public boolean canAccessDept(Long userId, Long targetDeptId, String dataScope) {
        if (userId == null || targetDeptId == null) {
            return false;
        }
        
        switch (dataScope) {
            case "ALL":
                return true;
            case "DEPT":
                Long userDeptId = getUserPrimaryDeptId(userId);
                return userDeptId != null && userDeptId.equals(targetDeptId);
            case "DEPT_AND_SUB":
                Long currentDeptId = getUserPrimaryDeptId(userId);
                if (currentDeptId == null) {
                    return false;
                }
                List<Long> accessibleDeptIds = getDeptAndSubDeptIds(currentDeptId);
                return accessibleDeptIds.contains(targetDeptId);
            case "SELF":
                return false; // 个人权限不能访问部门数据
            case "CUSTOM":
                // TODO: 实现自定义权限逻辑
                return false;
            default:
                return false;
        }
    }
    
    @Override
    public boolean canAccessUser(Long currentUserId, Long targetUserId, String dataScope) {
        if (currentUserId == null || targetUserId == null) {
            return false;
        }
        
        if (currentUserId.equals(targetUserId)) {
            return true;
        }
        
        switch (dataScope) {
            case "ALL":
                return true;
            case "DEPT":
                return isUsersInSameDept(currentUserId, targetUserId);
            case "DEPT_AND_SUB":
                return isUserInDeptOrSubDepts(currentUserId, targetUserId);
            case "SELF":
                return false; // 已经在上面检查过了
            case "CUSTOM":
                // TODO: 实现自定义权限逻辑
                return false;
            default:
                return false;
        }
    }
    
    @Override
    @Cacheable(value = "accessibleDeptIds", key = "#userId + ':' + #dataScope + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public List<Long> getAccessibleDeptIds(Long userId, String dataScope) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        Long tenantId = SecurityUtils.getTenantId();
        
        switch (dataScope) {
            case "ALL":
                return deptMapper.selectAllDeptIdsByTenantId(tenantId);
            case "DEPT":
                Long deptId = getUserPrimaryDeptId(userId);
                return deptId != null ? List.of(deptId) : new ArrayList<>();
            case "DEPT_AND_SUB":
                Long currentDeptId = getUserPrimaryDeptId(userId);
                return currentDeptId != null ? getDeptAndSubDeptIds(currentDeptId) : new ArrayList<>();
            case "SELF":
                return new ArrayList<>(); // 个人权限不能访问部门
            case "CUSTOM":
                // TODO: 实现自定义权限逻辑
                return new ArrayList<>();
            default:
                return new ArrayList<>();
        }
    }
    
    @Override
    @Cacheable(value = "accessibleUserIds", key = "#userId + ':' + #dataScope + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public List<Long> getAccessibleUserIds(Long userId, String dataScope) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        Long tenantId = SecurityUtils.getTenantId();
        
        switch (dataScope) {
            case "ALL":
                return userDeptMapper.selectAllUserIdsByTenantId(tenantId);
            case "DEPT":
            case "DEPT_AND_SUB":
                List<Long> accessibleDeptIds = getAccessibleDeptIds(userId, dataScope);
                return accessibleDeptIds.isEmpty() ? new ArrayList<>() : 
                       userDeptMapper.selectUserIdsByDeptIds(accessibleDeptIds, tenantId);
            case "SELF":
                return List.of(userId);
            case "CUSTOM":
                // TODO: 实现自定义权限逻辑
                return new ArrayList<>();
            default:
                return List.of(userId);
        }
    }
    
    @Override
    public Set<Long> filterAccessibleUserIds(Long currentUserId, List<Long> targetUserIds, String dataScope) {
        if (currentUserId == null || targetUserIds == null || targetUserIds.isEmpty()) {
            return new HashSet<>();
        }
        
        if ("ALL".equals(dataScope)) {
            return new HashSet<>(targetUserIds);
        }
        
        List<Long> accessibleUserIds = getAccessibleUserIds(currentUserId, dataScope);
        return targetUserIds.stream()
                .filter(accessibleUserIds::contains)
                .collect(Collectors.toSet());
    }
    
    @Override
    @Transactional
    @CacheEvict(value = {"userPrimaryDeptId", "userDeptIds"}, key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public boolean setPrimaryDept(Long userId, Long deptId) {
        if (userId == null || deptId == null) {
            return false;
        }
        
        try {
            Long tenantId = SecurityUtils.getTenantId();
            
            // 先将该用户的所有部门设置为非主部门
            userDeptMapper.updatePrimaryDept(userId, deptId, tenantId);
            
            log.info("设置用户主部门成功: userId={}, deptId={}", userId, deptId);
            return true;
        } catch (Exception e) {
            log.error("设置用户主部门失败: userId={}, deptId={}", userId, deptId, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    @CacheEvict(value = {"userPrimaryDeptId", "userDeptIds"}, key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public boolean addUserDept(Long userId, Long deptId, boolean isPrimary) {
        if (userId == null || deptId == null) {
            return false;
        }
        
        try {
            Long tenantId = SecurityUtils.getTenantId();
            
            // 检查关联是否已存在
            if (userDeptMapper.existsByUserIdAndDeptId(userId, deptId, tenantId)) {
                log.warn("用户部门关联已存在: userId={}, deptId={}", userId, deptId);
                return true;
            }
            
            // 如果设置为主部门，先清除其他主部门标识
            if (isPrimary) {
                userDeptMapper.updatePrimaryDept(userId, deptId, tenantId);
            }
            
            // 创建新的关联
            SysUserDept userDept = new SysUserDept();
            userDept.setUserId(userId);
            userDept.setDeptId(deptId);
            userDept.setIsPrimary(isPrimary ? 1 : 0);
            userDept.setTenantId(tenantId);
            userDept.setCreateBy(SecurityUtils.getUserId());
            userDept.setUpdateBy(SecurityUtils.getUserId());
            
            int result = userDeptMapper.insert(userDept);
            
            log.info("添加用户部门关联成功: userId={}, deptId={}, isPrimary={}", userId, deptId, isPrimary);
            return result > 0;
        } catch (Exception e) {
            log.error("添加用户部门关联失败: userId={}, deptId={}", userId, deptId, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    @CacheEvict(value = {"userPrimaryDeptId", "userDeptIds"}, key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public boolean removeUserDept(Long userId, Long deptId) {
        if (userId == null || deptId == null) {
            return false;
        }
        
        try {
            Long tenantId = SecurityUtils.getTenantId();
            int result = userDeptMapper.deleteByUserIdAndDeptId(userId, deptId, tenantId);
            
            log.info("移除用户部门关联成功: userId={}, deptId={}", userId, deptId);
            return result > 0;
        } catch (Exception e) {
            log.error("移除用户部门关联失败: userId={}, deptId={}", userId, deptId, e);
            return false;
        }
    }
    
    @Override
    @Cacheable(value = "deptUserIds", key = "#deptId + ':' + #includeSubDepts + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public List<Long> getDeptUserIds(Long deptId, boolean includeSubDepts) {
        if (deptId == null) {
            return new ArrayList<>();
        }
        
        Long tenantId = SecurityUtils.getTenantId();
        List<Long> deptIds;
        
        if (includeSubDepts) {
            deptIds = getDeptAndSubDeptIds(deptId);
        } else {
            deptIds = List.of(deptId);
        }
        
        return userDeptMapper.selectUserIdsByDeptIds(deptIds, tenantId);
    }
    
    /**
     * 检查用户是否在部门或子部门中
     */
    private boolean isUserInDeptOrSubDepts(Long currentUserId, Long targetUserId) {
        Long currentDeptId = getUserPrimaryDeptId(currentUserId);
        if (currentDeptId == null) {
            return false;
        }
        
        List<Long> targetUserDeptIds = getUserDeptIds(targetUserId);
        List<Long> accessibleDeptIds = getDeptAndSubDeptIds(currentDeptId);
        
        // 检查目标用户的任一部门是否在可访问部门列表中
        return targetUserDeptIds.stream().anyMatch(accessibleDeptIds::contains);
    }
    
    /**
     * 递归收集子部门ID
     */
    private void collectSubDeptIds(Long parentDeptId, Set<Long> deptIds) {
        Long tenantId = SecurityUtils.getTenantId();
        List<SysDept> subDepts = deptMapper.selectByParentId(parentDeptId, tenantId);
        
        for (SysDept subDept : subDepts) {
            if (!deptIds.contains(subDept.getId())) {
                deptIds.add(subDept.getId());
                // 递归获取子部门的子部门
                collectSubDeptIds(subDept.getId(), deptIds);
            }
        }
    }
}
