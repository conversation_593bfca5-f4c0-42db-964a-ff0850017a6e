package com.jcloud.common.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 租户实体类
 *
 * 注意：租户表不继承BaseEntity，因为租户表本身不需要tenantId字段
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Table("sys_tenant")
@Schema(description = "租户信息")
public class SysTenant {

    /**
     * 主键ID
     */
    @Id(keyType = KeyType.Auto)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 创建时间
     */
    @Column(onInsertValue = "now()")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(onInsertValue = "now()", onUpdateValue = "now()")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private Long createBy;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    private Long updateBy;

    /**
     * 逻辑删除标识（0-未删除，1-已删除）
     */
    @Column(isLogicDelete = true)
    @Schema(description = "删除标识")
    private Integer deleted = 0;

    /**
     * 版本号（乐观锁）
     */
    @Column(version = true)
    @Schema(description = "版本号")
    private Integer version = 0;
    
    /**
     * 租户编码
     */
    @Schema(description = "租户编码")
    private String tenantCode;
    
    /**
     * 租户名称
     */
    @Schema(description = "租户名称")
    private String tenantName;
    
    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String contactName;
    
    /**
     * 联系人电话
     */
    @Schema(description = "联系人电话")
    private String contactPhone;
    
    /**
     * 联系人邮箱
     */
    @Schema(description = "联系人邮箱")
    private String contactEmail;
    
    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态")
    private Integer status;
    
    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private LocalDateTime expireTime;
    
    /**
     * 最大用户数
     */
    @Schema(description = "最大用户数")
    private Integer maxUserCount;
    
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
