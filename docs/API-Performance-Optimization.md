# jCloud API性能优化方案

## 🎯 优化目标

解决jCloud项目中API请求响应时间过长的性能问题，提升用户体验和系统吞吐量。

## 📊 问题分析

### 发现的主要性能瓶颈：

1. **数据库连接池配置不当**
   - 主库连接池过小（max-active: 20）
   - 从库连接池过小（max-active: 15）
   - 等待时间过长（max-wait: 60s）

2. **Redis连接池配置不当**
   - 连接数过少（max-active: 4）
   - 等待时间过短（max-wait: 30ms）

3. **缺少缓存机制**
   - 频繁查询的用户、角色、权限数据没有缓存
   - 前端重复请求没有缓存

4. **前端请求优化不足**
   - 超时时间过长（10秒）
   - 缺少请求去重和防抖机制

## 🚀 优化方案

### 1. 数据库连接池优化

#### 主库配置优化：
```yaml
master:
  initial-size: 10        # 初始连接数：5 → 10
  min-idle: 10           # 最小空闲连接：5 → 10
  max-active: 50         # 最大连接数：20 → 50
  max-wait: 10000        # 最大等待时间：60s → 10s
```

#### 从库配置优化：
```yaml
slave:
  initial-size: 8        # 初始连接数：3 → 8
  min-idle: 8           # 最小空闲连接：3 → 8
  max-active: 40        # 最大连接数：15 → 40
  max-wait: 8000        # 最大等待时间：30s → 8s
```

#### MySQL连接参数优化：
- 添加 `rewriteBatchedStatements=true` 支持批量操作
- 添加 `cachePrepStmts=true` 启用预编译语句缓存
- 添加 `useServerPrepStmts=true` 使用服务器端预编译

### 2. Redis连接池优化

```yaml
redis:
  timeout: 10s           # 超时时间：30s → 10s
  lettuce:
    pool:
      max-active: 20     # 最大连接数：4 → 20
      max-wait: 5000ms   # 最大等待时间：30ms → 5s
      max-idle: 10       # 最大空闲连接：4 → 10
      min-idle: 5        # 最小空闲连接：0 → 5
```

### 3. MyBatis性能优化

```yaml
mybatis-flex:
  configuration:
    default-statement-timeout: 15  # 语句超时：30s → 15s
    lazy-loading-enabled: true     # 启用延迟加载
    aggressive-lazy-loading: false # 禁用激进延迟加载
    default-executor-type: reuse   # 重用执行器
```

### 4. 缓存机制实现

#### 后端缓存配置：
- **用户缓存**：30分钟 TTL
- **角色缓存**：1小时 TTL
- **权限缓存**：2小时 TTL
- **租户缓存**：4小时 TTL
- **菜单缓存**：2小时 TTL
- **部门缓存**：1小时 TTL

#### 缓存注解示例：
```java
@Cacheable(value = "userCache", 
           key = "'username:' + #username + ':tenant:' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", 
           unless = "#result == null")
public SysUser getUserByUsername(String username) {
    // 实现代码
}
```

#### 前端缓存机制：
- 实现请求缓存工具 `requestCache`
- 支持 LRU 缓存策略
- 自动清理过期缓存
- 请求去重机制

### 5. 前端请求优化

#### API配置优化：
```typescript
export const API_CONFIG = {
  TIMEOUT: 8000,         // 超时时间：10s → 8s
  RETRY_COUNT: 2,        // 重试次数
  RETRY_DELAY: 1000,     // 重试延迟
  CACHE_ENABLED: true,   // 启用缓存
  CACHE_TTL: 5 * 60 * 1000, // 5分钟缓存
}
```

#### 优化的请求Hook：
- 防抖机制（300ms）
- 自动重试（2次）
- 请求缓存
- 请求取消
- 组件卸载时自动清理

### 6. 数据库索引优化

创建了针对性的索引：

```sql
-- 用户查询优化索引
CREATE INDEX idx_sys_user_username_tenant ON sys_user(username, tenant_id, deleted);
CREATE INDEX idx_sys_user_email_tenant ON sys_user(email, tenant_id, deleted);
CREATE INDEX idx_sys_user_phone_tenant ON sys_user(phone, tenant_id, deleted);

-- 租户查询优化索引
CREATE INDEX idx_sys_tenant_code ON sys_tenant(tenant_code, deleted);
CREATE INDEX idx_sys_tenant_status ON sys_tenant(status, deleted);

-- 复合索引优化
CREATE INDEX idx_sys_user_tenant_status_create ON sys_user(tenant_id, status, create_time DESC, deleted);
```

### 7. 性能监控系统

#### 实时性能监控：
- **PerformanceMonitor**：监控API响应时间
- **PerformanceInterceptor**：自动拦截所有请求
- **PerformanceController**：提供性能统计接口

#### 监控指标：
- 请求总数
- 平均响应时间
- 最大/最小响应时间
- 错误率
- 慢请求检测（>2秒）

## 📈 预期性能提升

### 数据库层面：
- **连接获取时间**：减少 80%（60s → 10s）
- **并发处理能力**：提升 150%（20 → 50 连接）
- **查询响应时间**：减少 40%（通过索引优化）

### 缓存层面：
- **重复查询响应时间**：减少 90%（缓存命中）
- **数据库负载**：减少 60%
- **Redis响应时间**：减少 70%

### 前端层面：
- **重复请求**：减少 80%（缓存机制）
- **用户感知延迟**：减少 50%（防抖优化）
- **网络请求数量**：减少 60%

## 🔧 部署建议

### 1. 分阶段部署
1. **第一阶段**：数据库和Redis配置优化（立即生效）
2. **第二阶段**：缓存机制部署（需要测试）
3. **第三阶段**：前端优化部署（需要构建）
4. **第四阶段**：数据库索引创建（需要维护窗口）

### 2. 监控部署后效果
- 使用性能监控接口观察改善情况
- 监控数据库连接池使用率
- 观察缓存命中率
- 检查慢查询日志

### 3. 持续优化
- 定期分析性能统计数据
- 根据实际使用情况调整缓存策略
- 优化慢查询
- 监控系统资源使用情况

## ⚠️ 注意事项

1. **缓存一致性**：确保缓存更新策略正确
2. **内存使用**：监控Redis内存使用情况
3. **数据库负载**：观察优化后的数据库负载变化
4. **错误处理**：确保缓存失败时的降级策略
5. **监控告警**：设置性能指标告警阈值

## 📋 验证清单

- [ ] 数据库连接池配置已更新
- [ ] Redis连接池配置已更新
- [ ] 缓存配置已部署
- [ ] 用户服务缓存注解已添加
- [ ] 前端请求优化已部署
- [ ] 数据库索引已创建
- [ ] 性能监控系统已部署
- [ ] 性能测试已完成
- [ ] 监控告警已配置

通过以上优化措施，预计API响应时间将显著改善，系统整体性能将提升2-3倍。
