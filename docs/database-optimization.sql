-- jCloud 数据库性能优化 SQL
-- 建议的索引优化，提升查询性能

-- ================================
-- 用户表索引优化
-- ================================

-- 用户名查询索引（包含租户ID）
CREATE INDEX IF NOT EXISTS idx_sys_user_username_tenant ON sys_user(username, tenant_id, deleted);

-- 邮箱查询索引（包含租户ID）
CREATE INDEX IF NOT EXISTS idx_sys_user_email_tenant ON sys_user(email, tenant_id, deleted);

-- 手机号查询索引（包含租户ID）
CREATE INDEX IF NOT EXISTS idx_sys_user_phone_tenant ON sys_user(phone, tenant_id, deleted);

-- 用户状态查询索引
CREATE INDEX IF NOT EXISTS idx_sys_user_status_tenant ON sys_user(status, tenant_id, deleted);

-- 创建时间索引（用于排序）
CREATE INDEX IF NOT EXISTS idx_sys_user_create_time ON sys_user(create_time DESC);

-- 部门用户关联索引
CREATE INDEX IF NOT EXISTS idx_sys_user_dept_user_id ON sys_user_dept(user_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_dept_dept_id ON sys_user_dept(dept_id);

-- ================================
-- 租户表索引优化
-- ================================

-- 租户编码索引
CREATE INDEX IF NOT EXISTS idx_sys_tenant_code ON sys_tenant(tenant_code, deleted);

-- 租户状态索引
CREATE INDEX IF NOT EXISTS idx_sys_tenant_status ON sys_tenant(status, deleted);

-- 租户过期时间索引
CREATE INDEX IF NOT EXISTS idx_sys_tenant_expire_time ON sys_tenant(expire_time, deleted);

-- ================================
-- 角色表索引优化
-- ================================

-- 角色编码查询索引（包含租户ID）
CREATE INDEX IF NOT EXISTS idx_sys_role_code_tenant ON sys_role(role_code, tenant_id, deleted);

-- 角色状态索引
CREATE INDEX IF NOT EXISTS idx_sys_role_status_tenant ON sys_role(status, tenant_id, deleted);

-- 用户角色关联索引
CREATE INDEX IF NOT EXISTS idx_sys_user_role_user_id ON sys_user_role(user_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_role_role_id ON sys_user_role(role_id);

-- ================================
-- 权限表索引优化
-- ================================

-- 权限编码索引
CREATE INDEX IF NOT EXISTS idx_sys_permission_code ON sys_permission(permission_code, deleted);

-- 权限类型索引
CREATE INDEX IF NOT EXISTS idx_sys_permission_type ON sys_permission(permission_type, deleted);

-- 角色权限关联索引
CREATE INDEX IF NOT EXISTS idx_sys_role_permission_role_id ON sys_role_permission(role_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_permission_permission_id ON sys_role_permission(permission_id);

-- ================================
-- 菜单表索引优化
-- ================================

-- 父级菜单索引
CREATE INDEX IF NOT EXISTS idx_sys_menu_parent_id ON sys_menu(parent_id, deleted);

-- 菜单类型索引
CREATE INDEX IF NOT EXISTS idx_sys_menu_type ON sys_menu(menu_type, deleted);

-- 菜单状态索引
CREATE INDEX IF NOT EXISTS idx_sys_menu_status ON sys_menu(status, deleted);

-- 菜单排序索引
CREATE INDEX IF NOT EXISTS idx_sys_menu_sort ON sys_menu(sort_order);

-- ================================
-- 部门表索引优化
-- ================================

-- 父级部门索引
CREATE INDEX IF NOT EXISTS idx_sys_dept_parent_id ON sys_dept(parent_id, tenant_id, deleted);

-- 部门状态索引
CREATE INDEX IF NOT EXISTS idx_sys_dept_status_tenant ON sys_dept(status, tenant_id, deleted);

-- ================================
-- SQL审计日志表索引优化
-- ================================

-- 创建时间索引（用于清理和查询）
CREATE INDEX IF NOT EXISTS idx_sql_audit_log_create_time ON sys_sql_audit_log(create_time);

-- 慢SQL查询索引
CREATE INDEX IF NOT EXISTS idx_sql_audit_log_slow_sql ON sys_sql_audit_log(is_slow_sql, create_time);

-- 用户查询索引
CREATE INDEX IF NOT EXISTS idx_sql_audit_log_user ON sys_sql_audit_log(user, create_time);

-- 租户查询索引
CREATE INDEX IF NOT EXISTS idx_sql_audit_log_tenant ON sys_sql_audit_log(tenant_id, create_time);

-- 执行时间索引（用于性能分析）
CREATE INDEX IF NOT EXISTS idx_sql_audit_log_execution_time ON sys_sql_audit_log(execution_time DESC);

-- ================================
-- 复合索引优化建议
-- ================================

-- 用户查询常用复合索引
CREATE INDEX IF NOT EXISTS idx_sys_user_tenant_status_create ON sys_user(tenant_id, status, create_time DESC, deleted);

-- 租户状态和过期时间复合索引
CREATE INDEX IF NOT EXISTS idx_sys_tenant_status_expire ON sys_tenant(status, expire_time, deleted);

-- 角色权限查询复合索引
CREATE INDEX IF NOT EXISTS idx_sys_role_tenant_status ON sys_role(tenant_id, status, deleted);

-- ================================
-- 性能优化建议
-- ================================

-- 1. 定期分析表统计信息
-- ANALYZE TABLE sys_user, sys_tenant, sys_role, sys_permission, sys_menu, sys_dept;

-- 2. 定期优化表
-- OPTIMIZE TABLE sys_user, sys_tenant, sys_role, sys_permission, sys_menu, sys_dept;

-- 3. 监控慢查询
-- SET GLOBAL slow_query_log = 'ON';
-- SET GLOBAL long_query_time = 1;

-- 4. 配置查询缓存（如果使用MySQL 5.7及以下版本）
-- SET GLOBAL query_cache_type = ON;
-- SET GLOBAL query_cache_size = 268435456; -- 256MB

-- ================================
-- 分区表建议（适用于大数据量）
-- ================================

-- SQL审计日志表按月分区（示例）
/*
ALTER TABLE sys_sql_audit_log PARTITION BY RANGE (YEAR(create_time) * 100 + MONTH(create_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    -- 继续添加更多分区...
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/
